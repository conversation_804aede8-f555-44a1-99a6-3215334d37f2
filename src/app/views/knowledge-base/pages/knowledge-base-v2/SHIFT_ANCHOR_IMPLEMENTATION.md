# Shift Anchor System Implementation

## Overview

The Shift anchor system provides a more intuitive range selection experience where the first Shift+clicked item remains the anchor point for all subsequent Shift+Click operations until the Shift key is released.

## Problem Solved

**Previous Behavior:**
- Range selection used `lastSelectedItem()` as anchor
- Anchor changed after each selection operation
- Multiple Shift+Click operations were unpredictable

**New Behavior:**
- First Shift+Click sets a persistent anchor
- All subsequent Shift+Click operations use the same anchor
- Anchor is cleared only when Shift key is released

## Implementation Details

### 1. **New Signal Property**
```typescript
// Shift anchor for consistent range selection
shiftAnchorItem = signal<{ item: IFolder | IFile; type: 'folder' | 'file'; index: number } | null>(null);
```

### 2. **Enhanced Keyboard Event Listeners**
```typescript
private listenKeyboardEvent() {
  this.renderer2.listen('document', 'keydown', (event: KeyboardEvent) => {
    if (event.key === 'Shift') {
      const wasShiftPressed = this.isShiftPressed();
      this.isShiftPressed.set(true);
      
      if (!wasShiftPressed) {
        // Shift key was just pressed (transition from released to pressed)
        console.log('⌨️ Shift key pressed - Range selection mode enabled, ready to set anchor');
      }
    }
  });
  
  this.renderer2.listen('document', 'keyup', (event: KeyboardEvent) => {
    if (event.key === 'Shift') {
      this.isShiftPressed.set(false);
      this.clearShiftAnchor(); // Clear anchor when Shift is released
      console.log('⌨️ Shift key released - Range selection mode disabled, anchor cleared');
    }
  });
}
```

### 3. **New Shift Anchor Management Methods**

#### **setShiftAnchor()**
```typescript
private setShiftAnchor(item: IFolder | IFile, type: 'folder' | 'file') {
  const index = this.getItemIndex(item, type);
  this.shiftAnchorItem.set({ item, type, index });
  console.log('🎯 Shift anchor set:', {
    item: { id: item.id ?? 'undefined', name: item.name },
    type,
    index
  });
}
```

#### **clearShiftAnchor()**
```typescript
private clearShiftAnchor() {
  const previousAnchor = this.shiftAnchorItem();
  this.shiftAnchorItem.set(null);
  if (previousAnchor) {
    console.log('🎯 Shift anchor cleared:', {
      previousAnchor: { id: previousAnchor.item.id ?? 'undefined', name: previousAnchor.item.name }
    });
  }
}
```

#### **handleRangeSelectionFromAnchor()**
```typescript
private handleRangeSelectionFromAnchor(item: IFolder | IFile, type: 'folder' | 'file') {
  const shiftAnchor = this.shiftAnchorItem();
  if (!shiftAnchor) {
    // Fallback to single selection if no anchor
    this.handleSingleSelection(item, type);
    return;
  }

  // Clear current selections
  this.folderSelection.clear();
  this.fileSelection.clear();

  // Calculate range from anchor to current item
  const currentIndex = this.getItemIndex(item, type);
  const anchorIndex = shiftAnchor.index;
  const startIndex = Math.min(anchorIndex, currentIndex);
  const endIndex = Math.max(anchorIndex, currentIndex);

  // Select all items in range
  // ... (selection logic)
}
```

### 4. **Updated Selection Logic**

#### **selectFolder() Method**
```typescript
if (isShift) {
  if (!this.shiftAnchorItem()) {
    // First Shift+Click: set as anchor and select normally
    this.setShiftAnchor(folder, 'folder');
    this.handleSingleSelection(folder, 'folder');
  } else {
    // Subsequent Shift+Click: range select from anchor to current item
    this.handleRangeSelectionFromAnchor(folder, 'folder');
  }
} else if (isCtrl) {
  this.handleMultiSelection(folder, 'folder');
} else {
  this.handleSingleSelection(folder, 'folder');
}
```

## User Experience Flow

### **Scenario 1: Basic Range Selection**
1. User clicks Item A (normal selection) → Item A selected
2. User holds Shift and clicks Item C → Item C becomes Shift anchor, range selects A to C
3. User (still holding Shift) clicks Item F → Range selects from C (Shift anchor) to F
4. User releases Shift → Anchor cleared

### **Scenario 2: Multiple Range Operations**
1. User holds Shift and clicks Item B → Item B becomes anchor and is selected
2. User (still holding Shift) clicks Item E → Range selects B to E
3. User (still holding Shift) clicks Item A → Range selects B to A (reverse direction)
4. User (still holding Shift) clicks Item G → Range selects B to G
5. User releases Shift → Anchor cleared

### **Scenario 3: Mixed Selection Modes**
1. User clicks Item A → Item A selected
2. User holds Ctrl and clicks Item C → Items A and C selected
3. User releases Ctrl, holds Shift, clicks Item E → Item E becomes anchor, selects only E
4. User (still holding Shift) clicks Item H → Range selects E to H

## Console Logging

### **Shift Key Events**
```
⌨️ Shift key pressed - Range selection mode enabled, ready to set anchor
⌨️ Shift key released - Range selection mode disabled, anchor cleared
```

### **Anchor Management**
```
🎯 Shift anchor set: {
  item: { id: 123, name: "Folder 1" },
  type: "folder",
  index: 0
}

🎯 Shift anchor cleared: {
  previousAnchor: { id: 123, name: "Folder 1" }
}
```

### **Range Selection from Anchor**
```
📏 Range selection from Shift anchor: {
  anchor: { id: 123, name: "Folder 1", type: "folder", index: 0 },
  target: { id: 456, name: "File 3", type: "file", index: 7 },
  folderCount: 5,
  fileCount: 3
}

📊 Range selection indices from anchor: {
  startIndex: 0,
  endIndex: 7,
  totalItems: 8
}

✅ Range selection from anchor completed: {
  selectedItems: [...],
  totalFolders: 5,
  totalFiles: 3,
  totalSelected: 8
}
```

## Edge Cases Handled

### **1. No Anchor Set**
- If Shift+Click occurs without an anchor, falls back to single selection
- Logs warning and sets the clicked item as new anchor

### **2. Anchor Item Deleted**
- If anchor item is no longer available, falls back to single selection
- Graceful degradation without errors

### **3. Shift Key State Transitions**
- Detects when Shift transitions from released to pressed
- Only clears anchor when Shift is actually released

### **4. Mixed Keyboard Modifiers**
- Ctrl+Click works independently of Shift anchor
- Normal clicks don't affect Shift anchor (only Shift release clears it)

## Benefits

1. **Predictable Behavior**: Users know the anchor won't change during Shift session
2. **Intuitive UX**: Matches Windows File Explorer behavior
3. **Flexible Range Selection**: Can select ranges in any direction from anchor
4. **Clear State Management**: Anchor is automatically cleared when Shift is released
5. **Debugging Support**: Comprehensive logging for troubleshooting

## Testing Scenarios

### **Basic Functionality**
- [ ] First Shift+Click sets anchor and selects item
- [ ] Subsequent Shift+Click selects range from anchor
- [ ] Shift release clears anchor
- [ ] Works in both grid and list views

### **Edge Cases**
- [ ] Shift+Click without previous selection
- [ ] Multiple range selections with same anchor
- [ ] Reverse direction range selection
- [ ] Mixed with Ctrl+Click operations
- [ ] Anchor item deletion handling

### **Visual Feedback**
- [ ] Selected items are highlighted correctly
- [ ] Range selection updates immediately
- [ ] Console logs provide clear debugging info
- [ ] UI updates properly after anchor changes
