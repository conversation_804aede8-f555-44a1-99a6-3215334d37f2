# Windows File Explorer-Style Selection Implementation

## Overview

This implementation adds Windows File Explorer-style selection behavior to the knowledge-base-v2 component, supporting three interaction patterns:

1. **Single Click Selection** - Select one item, deselect others
2. **Ctrl+Click Multi-Selection** - Add/remove items from selection
3. **Shift+Click Range Selection** - Select range of items

## Architecture

### Parent Component (knowledge-base-v2.component.ts)

**New Properties:**
- `lastSelectedItem` - Signal tracking the anchor point for range selection
- Enhanced keyboard state tracking with existing `isShiftPressed` and `isCtrlPressed` signals

**Modified Methods:**
- `selectFolder(folder, event?)` - Now handles different selection modes
- `selectFile(file, event?)` - Now handles different selection modes
- `onFolderSelected(data)` - Updated to receive event data
- `onFileSelected(data)` - Updated to receive event data

**New Helper Methods:**
- `handleSingleSelection()` - Clears all, selects one item
- `handleMultiSelection()` - Toggles item selection (Ctrl+Click)
- `handleRangeSelection()` - Selects range of items (Shift+Click)
- `getItemIndex()` - Gets index of item in combined list
- `getCombinedList()` - Returns folders + files with isFolder flag

### Child Components (grid-view & list-view)

**New Inputs:**
- `isShiftPressed` - Signal from parent for keyboard state
- `isCtrlPressed` - Signal from parent for keyboard state

**Modified Outputs:**
- `folderSelected` - Now emits `{folder, event?}`
- `fileSelected` - Now emits `{file, event?}`

**Updated Click Handlers:**
- `onFolderClick(folder, event?)` - Passes mouse event
- `onFileClick(file, event?)` - Passes mouse event

## Selection Behavior

### Single Click Selection
```typescript
// Clear all selections, select clicked item
this.folderSelection.clear();
this.fileSelection.clear();
this.folderSelection.select(folder); // or fileSelection.select(file)
```

### Ctrl+Click Multi-Selection
```typescript
// Toggle selection of clicked item, keep others
this.folderSelection.toggle(folder); // or fileSelection.toggle(file)
```

### Shift+Click Range Selection
```typescript
// Clear all, select range from last anchor to current item
const startIndex = Math.min(lastIndex, currentIndex);
const endIndex = Math.max(lastIndex, currentIndex);
// Select all items in range [startIndex, endIndex]
```

## Visual Feedback

Selection state is maintained through Angular CDK's `SelectionModel` and reflected in templates via:
- `folderSelection().isSelected(folder)`
- `fileSelection().isSelected(file)`

CSS classes applied based on selection state:
- **Grid View**: `bg-light-hover dark:bg-dark-hover` for selected folders
- **List View**: `folder-selected` and `file-selected` classes

## Edge Cases Handled

1. **Shift+Click without previous selection** - Behaves like normal click
2. **Clicking outside items** - Clears all selections via mouse event listener
3. **Mixed folder/file selection** - Cross-type selections clear previous type
4. **Range selection across different item types** - Handles folders and files in combined list

## Testing

The implementation includes comprehensive test coverage in `selection-test.spec.ts` covering:
- Single click behavior
- Ctrl+Click multi-selection
- Shift+Click range selection
- Mixed selection scenarios
- Clear selection functionality

## Usage Example

```html
<!-- Parent template -->
<app-grid-view
  [isShiftPressed]="isShiftPressed"
  [isCtrlPressed]="isCtrlPressed"
  (folderSelected)="onFolderSelected($event)"
  (fileSelected)="onFileSelected($event)">
</app-grid-view>
```

```html
<!-- Child template -->
<div (click)="onFolderClick(folder, $event)">
  <!-- folder content -->
</div>
```

## Performance Considerations

- Uses Angular signals for reactive state management
- Leverages CDK SelectionModel for efficient selection tracking
- Minimal DOM updates through OnPush change detection
- Event delegation for keyboard state management

## Accessibility

- Maintains keyboard navigation compatibility
- Preserves existing screen reader support
- Visual selection indicators remain accessible
- Focus management preserved during selection changes
