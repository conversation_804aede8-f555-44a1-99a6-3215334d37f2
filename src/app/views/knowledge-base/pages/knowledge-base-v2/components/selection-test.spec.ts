import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SelectionModel } from '@angular/cdk/collections';
import { signal } from '@angular/core';
import { KnowledgeBaseV2Component } from '../knowledge-base-v2.component';
import { IFile, IFolder } from '@shared/models';

describe('Windows File Explorer Selection Mechanism', () => {
  let component: KnowledgeBaseV2Component;
  let fixture: ComponentFixture<KnowledgeBaseV2Component>;
  let folderSelection: SelectionModel<IFolder>;
  let fileSelection: SelectionModel<IFile>;

  const mockFolders: IFolder[] = [
    { id: 1, ai_id: 'ai1', parent_id: null, name: 'Folder 1', isFolder: true },
    { id: 2, ai_id: 'ai2', parent_id: null, name: 'Folder 2', isFolder: true },
    { id: 3, ai_id: 'ai3', parent_id: null, name: 'Folder 3', isFolder: true },
    { id: 4, ai_id: 'ai4', parent_id: null, name: 'Folder 4', isFolder: true },
  ];

  const mockFiles: IFile[] = [
    {
      id: 1, folder_id: null, name: 'File 1.pdf', ext: 'pdf',
      metadata_columns: '', collection_id: 1, size: 1024,
      text_content: '', url: '', file_path: '', status: 'COMPLETED',
      created_at: '2024-01-01', updated_at: '2024-01-01', isFolder: false
    },
    {
      id: 2, folder_id: null, name: 'File 2.txt', ext: 'txt',
      metadata_columns: '', collection_id: 1, size: 2048,
      text_content: '', url: '', file_path: '', status: 'COMPLETED',
      created_at: '2024-01-02', updated_at: '2024-01-02', isFolder: false
    },
    {
      id: 3, folder_id: null, name: 'File 3.csv', ext: 'csv',
      metadata_columns: '', collection_id: 1, size: 3072,
      text_content: '', url: '', file_path: '', status: 'COMPLETED',
      created_at: '2024-01-03', updated_at: '2024-01-03', isFolder: false
    }
  ];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [KnowledgeBaseV2Component]
    }).compileComponents();

    fixture = TestBed.createComponent(KnowledgeBaseV2Component);
    component = fixture.componentInstance;
    
    // Setup mock data
    component.folderList.set(mockFolders);
    component.fileList.set(mockFiles);
    
    folderSelection = component.folderSelection;
    fileSelection = component.fileSelection;
    
    fixture.detectChanges();
  });

  describe('Single Click Selection', () => {
    it('should select only the clicked folder and deselect others', () => {
      // Select first folder
      component.onFolderSelected({ folder: mockFolders[0] });
      expect(folderSelection.selected.length).toBe(1);
      expect(folderSelection.isSelected(mockFolders[0])).toBe(true);

      // Select second folder - should deselect first
      component.onFolderSelected({ folder: mockFolders[1] });
      expect(folderSelection.selected.length).toBe(1);
      expect(folderSelection.isSelected(mockFolders[0])).toBe(false);
      expect(folderSelection.isSelected(mockFolders[1])).toBe(true);
    });

    it('should select only the clicked file and deselect others', () => {
      // Select first file
      component.onFileSelected({ file: mockFiles[0] });
      expect(fileSelection.selected.length).toBe(1);
      expect(fileSelection.isSelected(mockFiles[0])).toBe(true);

      // Select second file - should deselect first
      component.onFileSelected({ file: mockFiles[1] });
      expect(fileSelection.selected.length).toBe(1);
      expect(fileSelection.isSelected(mockFiles[0])).toBe(false);
      expect(fileSelection.isSelected(mockFiles[1])).toBe(true);
    });

    it('should clear file selection when selecting folder and vice versa', () => {
      // Select a file first
      component.onFileSelected({ file: mockFiles[0] });
      expect(fileSelection.selected.length).toBe(1);

      // Select a folder - should clear file selection
      component.onFolderSelected({ folder: mockFolders[0] });
      expect(fileSelection.selected.length).toBe(0);
      expect(folderSelection.selected.length).toBe(1);
    });
  });

  describe('Ctrl+Click Multi-Selection', () => {
    beforeEach(() => {
      // Simulate Ctrl key pressed
      component.isCtrlPressed.set(true);
    });

    it('should build multi-selection of folders with Ctrl+Click', () => {
      // Ctrl+Click folder 1
      component.onFolderSelected({ folder: mockFolders[0] });
      expect(folderSelection.selected.length).toBe(1);
      expect(folderSelection.isSelected(mockFolders[0])).toBe(true);

      // Ctrl+Click folder 3 (non-contiguous)
      component.onFolderSelected({ folder: mockFolders[2] });
      expect(folderSelection.selected.length).toBe(2);
      expect(folderSelection.isSelected(mockFolders[0])).toBe(true);
      expect(folderSelection.isSelected(mockFolders[2])).toBe(true);

      // Ctrl+Click folder 1 again (should deselect)
      component.onFolderSelected({ folder: mockFolders[0] });
      expect(folderSelection.selected.length).toBe(1);
      expect(folderSelection.isSelected(mockFolders[0])).toBe(false);
      expect(folderSelection.isSelected(mockFolders[2])).toBe(true);
    });

    it('should build multi-selection of files with Ctrl+Click', () => {
      // Ctrl+Click file 1
      component.onFileSelected({ file: mockFiles[0] });
      expect(fileSelection.selected.length).toBe(1);
      expect(fileSelection.isSelected(mockFiles[0])).toBe(true);

      // Ctrl+Click file 3 (non-contiguous)
      component.onFileSelected({ file: mockFiles[2] });
      expect(fileSelection.selected.length).toBe(2);
      expect(fileSelection.isSelected(mockFiles[0])).toBe(true);
      expect(fileSelection.isSelected(mockFiles[2])).toBe(true);
    });
  });

  describe('Shift+Click Range Selection', () => {
    beforeEach(() => {
      // Simulate Shift key pressed
      component.isShiftPressed.set(true);
    });

    it('should select range of folders with Shift+Click', () => {
      // First click to establish anchor
      component.isShiftPressed.set(false);
      component.onFolderSelected({ folder: mockFolders[0] });
      
      // Shift+Click to select range
      component.isShiftPressed.set(true);
      component.onFolderSelected({ folder: mockFolders[2] });
      
      // Should select folders 0, 1, 2
      expect(folderSelection.selected.length).toBe(3);
      expect(folderSelection.isSelected(mockFolders[0])).toBe(true);
      expect(folderSelection.isSelected(mockFolders[1])).toBe(true);
      expect(folderSelection.isSelected(mockFolders[2])).toBe(true);
    });

    it('should handle Shift+Click when no previous selection exists', () => {
      // Shift+Click without previous selection should behave like normal click
      component.onFolderSelected({ folder: mockFolders[1] });
      expect(folderSelection.selected.length).toBe(1);
      expect(folderSelection.isSelected(mockFolders[1])).toBe(true);
    });
  });

  describe('Mixed Selection Scenarios', () => {
    it('should handle switching between different selection modes', () => {
      // Start with normal click
      component.onFolderSelected({ folder: mockFolders[0] });
      
      // Switch to Ctrl+Click
      component.isCtrlPressed.set(true);
      component.onFolderSelected({ folder: mockFolders[2] });
      expect(folderSelection.selected.length).toBe(2);
      
      // Switch to Shift+Click
      component.isCtrlPressed.set(false);
      component.isShiftPressed.set(true);
      component.onFolderSelected({ folder: mockFolders[3] });
      
      // Should select range from last anchor (folder 2) to folder 3
      expect(folderSelection.selected.length).toBe(2);
      expect(folderSelection.isSelected(mockFolders[2])).toBe(true);
      expect(folderSelection.isSelected(mockFolders[3])).toBe(true);
    });
  });

  describe('Clear Selection', () => {
    it('should clear all selections when clearAllSelectedFolderAndFile is called', () => {
      // Select some items
      component.onFolderSelected({ folder: mockFolders[0] });
      component.onFileSelected({ file: mockFiles[0] });
      
      expect(folderSelection.selected.length).toBe(1);
      expect(fileSelection.selected.length).toBe(1);
      
      // Clear all
      component.clearAllSelectedFolderAndFile();
      
      expect(folderSelection.selected.length).toBe(0);
      expect(fileSelection.selected.length).toBe(0);
      expect(component.lastSelectedItem()).toBeNull();
    });
  });
});
