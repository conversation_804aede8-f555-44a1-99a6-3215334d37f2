<div class="py-3 h-full flex flex-col overflow-hidden">
  <div class="h-full flex flex-col flex-grow overflow-hidden">
    <!-- Folders Section -->
    @if (folderList().length > 0) {
    <div class="flex flex-col items-stretch justify-start space-y-2"
      style="min-height: 150px; max-height: 40%">
      <div class="w-full px-5 h-14 flex items-center gap-2 !text-base-content dark:!text-dark-base-content text-xl font-bold">
          Folders ({{ folderList().length }})
          <app-svg-icon type="icSync"
                        class="w-5 h-5 hover:!text-blue-500 cursor-pointer"
                        (click)="refreshVirtualScrollViewports()"></app-svg-icon>
      </div>
      <cdk-virtual-scroll-viewport #folderViewport id="folderViewport" itemSize="50" minBufferPx="60" maxBufferPx="400"
        class="h-full overflow-y-auto">
        <div
          class="w-full grid grid-cols-1 xs:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 2xl:grid-cols-5 gap-4 pl-5 file-folder-list-wrapper">
          <ng-container *cdkVirtualFor="
              let folder of folderList();
              let i = index;
              trackBy: trackByFolderId
            ">
            <div (dblclick)="onFolderDoubleClick(folder)"
                  appLongPress
                  (longPress)="onFolderDoubleClick(folder)"
                  (click)="onFolderClick(folder, $event)"
                  (contextmenu)="onRightClick(folder, $event)"
                  [ngClass]="
                  folderSelection().isSelected(folder)
                    ? 'bg-light-hover dark:bg-dark-hover text-base-content dark:text-dark-base-content'
                    : 'hover:cursor-pointer hover:bg-base-400 dark:hover:bg-dark-base-400'"
                 [attr.data-folder-id]="folder.id"
              class="col-span-1 py-4 px-2 w-full rounded-2xl flex flex-col items-stretch folder-item bg-base-100 dark:bg-dark-base-100">
              <div class="flex flex-grow-0 flex-shrink-0 items-center justify-between file-folder-wrapper">
                  <app-svg-icon type="icFolderFilled"
                                class="flex-shrink-0 flex-none w-6 h-6"
                                (dblclick)="$event.stopPropagation(); onFolderDoubleClick(folder)"
                  ></app-svg-icon>
                  <div class="flex-grow truncate folder-name text-base-content dark:text-dark-base-content"
                       (dblclick)="$event.stopPropagation();onFolderDoubleClick(folder)"
                       [dxTooltip]="folder?.name ?? ''" dxTooltipPosition="above">
                    {{ folder.name }}
                  </div>
                  <div class="flex-shrink-0 flex-none flex items-center justify-end">
                    <app-svg-icon
                      type="icMoreHorizontal"
                      cdkOverlayOrigin
                      #trigger="cdkOverlayOrigin"
                      (click)="$event.stopPropagation(); openMenuActions(folder)"
                      class="cursor-pointer w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content"
                    >
                    </app-svg-icon>
                    <ng-template
                      cdkConnectedOverlay
                      [cdkConnectedOverlayOrigin]="trigger"
                      [cdkConnectedOverlayOpen]="
                    itemSelected()?.id === folder.id &&
                    itemSelected()?.type === getType(folder)
                  "
                      [cdkConnectedOverlayPush]="true"
                      [cdkConnectedOverlayPositions]="[
                    {
                      originX: 'start',
                      originY: 'center',
                      overlayX: 'end',
                      overlayY: 'top',
                      offsetY: 10
                    },
                    {
                      originX: 'start',
                      originY: 'center',
                      overlayX: 'end',
                      overlayY: 'bottom',
                      offsetY: 10,
                    },
                  ]"
                    >
                      <ul
                        class="w-[245px] flex flex-col justify-between p-1 z-50 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
                        (clickOutside)="closeAllMenus()"
                      >
                        <li>
                          <button
                            (click)="$event.stopPropagation(); onFolderRename(folder)"
                            class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                          >
                            <app-svg-icon
                              type="icEdit"
                              class="w-6 h-6 flex items-center justify-center"
                            ></app-svg-icon>
                            <div
                              class="flex items-center justify-between text-[16px] font-medium"
                            >
                              Rename
                            </div>
                          </button>
                        </li>
                        <li>
                          <button
                            (click)="$event.stopPropagation(); onFolderDelete(folder)"
                            class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                          >
                            <app-svg-icon
                              type="icTrash"
                              class="w-6 h-6 flex items-center justify-center"
                            ></app-svg-icon>
                            <div
                              class="flex items-center justify-between text-[16px] font-medium"
                            >
                              Delete
                            </div>
                          </button>
                        </li>
                      </ul>
                    </ng-template>
                  </div>
              </div>
            </div>
          </ng-container>
        </div>
      </cdk-virtual-scroll-viewport>
    </div>
    }

    <!-- Files Section -->
    @if (fileList().length > 0) {
    <div class="flex flex-col items-stretch justify-start space-y-2 mt-4 flex-grow"
      style="height: 60%; min-height: 200px">
      <div class="w-full px-5 h-14 flex items-center gap-2 !text-base-content dark:!text-dark-base-content text-xl font-bold">
          Files ({{ fileList().length }})
      </div>
      <cdk-virtual-scroll-viewport #fileViewport id="fileViewport" itemSize="50" minBufferPx="200" maxBufferPx="400"
        style="height: calc(100% - 56px); overflow: auto" class="h-full">
        <div
          class="w-full grid grid-cols-1 xs:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 2xl:grid-cols-5 pl-5 file-folder-list-wrapper">
          <ng-container *cdkVirtualFor="
              let file of fileList();
              trackBy: trackByFileId
            ">
            <div
            (dblclick)="onFileDoubleClick(file)"
            appLongPress
            (longPress)="onFileDoubleClick(file)"
            (click)="onFileClick(file, $event)"
            (contextmenu)="onRightClick(file, $event)"
            [ngClass]="
                fileSelection().isSelected(file)
                  ? 'bg-base-100 dark:bg-dark-base-100'
                  : 'hover:cursor-pointer hover:bg-base-400 dark:hover:bg-dark-base-400'
              " draggable="true" [attr.data-file-id]="file.id"
              class="col-span-1 min-h-60 w-full gap-4 rounded-2xl pt-4 px-2 pb-2 flex flex-col items-stretch bg-base-100 dark:bg-dark-base-100">
              <div class="flex flex-shrink-0 items-center justify-between file-folder-wrapper">
                <!-- File Status Icon -->
                <app-svg-icon [type]="getFileIcon(file)"
                              class="flex-shrink-0 flex-none w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content"></app-svg-icon>
                <div class="flex-grow truncate text-base-content dark:text-dark-base-content"
                     [dxTooltip]="file?.name ?? ''" dxTooltipPosition="above">
                  {{ file.name }}
                </div>
<!--                menu trigger-->
                <div class="flex-shrink-0 flex-none flex items-center justify-end">
                  <app-svg-icon
                    type="icMoreHorizontal"
                    cdkOverlayOrigin
                    #trigger="cdkOverlayOrigin"
                    (click)="$event.stopPropagation(); openMenuActions(file)"
                    class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
                  >
                  </app-svg-icon>
                  <ng-template
                    cdkConnectedOverlay
                    [cdkConnectedOverlayOrigin]="trigger"
                    [cdkConnectedOverlayOpen]="
                  itemSelected()?.id === file.id &&
                  itemSelected()?.type === getType(file)
                "
                    [cdkConnectedOverlayPush]="true"
                    [cdkConnectedOverlayPositions]="[
                  {
                    originX: 'start',
                    originY: 'center',
                    overlayX: 'end',
                    overlayY: 'top',
                    offsetY: 10
                  },
                  {
                    originX: 'start',
                    originY: 'center',
                    overlayX: 'end',
                    overlayY: 'bottom',
                    offsetY: 10,
                  },
                ]"
                  >
                    <ul
                      class="w-[166px] flex flex-col justify-between p-1 z-50 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
                      (clickOutside)="closeAllMenus()"
                    >
                      <li>
                        <button
                          (click)="$event.stopPropagation(); onFileInfo(file)"
                          class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                        >
                          <app-svg-icon
                            type="icInfo"
                            class="w-6 h-6 flex items-center justify-center"
                          ></app-svg-icon>
                          <div
                            class="flex items-center justify-between text-[16px] font-medium"
                          >
                            Info
                          </div>
                        </button>
                      </li>
                      <li>
                        <button
                          (click)="$event.stopPropagation(); onFileRename(file)"
                          class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                        >
                          <app-svg-icon
                            type="icEdit"
                            class="w-6 h-6 flex items-center justify-center"
                          ></app-svg-icon>
                          <div
                            class="flex items-center justify-between text-[16px] font-medium"
                          >
                            Rename
                          </div>
                        </button>
                      </li>
                      <li>
                        <button
                          (click)="$event.stopPropagation(); onFileMove(file)"
                          class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                        >
                          <app-svg-icon
                            type="icFolderMove"
                            class="w-6 h-6 flex items-center justify-center"
                          ></app-svg-icon>
                          <div
                            class="flex items-center justify-between text-[16px] font-medium"
                          >
                            Move
                          </div>
                        </button>
                      </li>
                      <li>
                        <button
                          (click)="$event.stopPropagation(); onFileDelete(file)"
                          class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                        >
                          <app-svg-icon
                            type="icTrash"
                            class="w-6 h-6 flex items-center justify-center"
                          ></app-svg-icon>
                          <div
                            class="flex items-center justify-between text-[16px] font-medium"
                          >
                            Delete
                          </div>
                        </button>
                      </li>
                    </ul>
                  </ng-template>
                </div>
              </div>
              <div class="flex-1 h-full w-full bg-base-200 dark:bg-dark-base-200 flex flex-col p-2 rounded-lg">
                <div class="flex-1 w-full h-full flex items-center justify-center">
                  <app-svg-icon [type]="getFileIcon(file)" class="w-16 h-16 flex !text-neutral-content dark:!text-dark-neutral-content"></app-svg-icon>
                </div>
                <div class="items-center self-end">
                  <div class="text-xs px-2 py-1 rounded-full inline-block"
                       [ngClass]="'bg-' + getStatusColor(file.status)+ ' dark:bg-dark-' + getStatusColor(file.status)">
                    {{ file.status | titlecase }}
                  </div>
                </div>
              </div>
            </div>
          </ng-container>
        </div>
      </cdk-virtual-scroll-viewport>
    </div>
    }
  </div>
</div>

<!-- Context Menu Overlay -->
@if (contextMenuItem() && contextMenuPosition()) {
<div
  class="fixed inset-0 z-40"
  (click)="closeContextMenu()"
  style="background: transparent;"
></div>

<div
  class="fixed z-50"
  [style.left.px]="contextMenuPosition()!.x"
  [style.top.px]="contextMenuPosition()!.y"
>
  @if (contextMenuItem()!.type === 'folder') {
  <!-- Folder Context Menu -->
  <div
    class="w-[245px] flex flex-col justify-between p-1 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
    (clickOutside)="closeContextMenu()"
  >
    <button
      (click)="$event.stopPropagation(); onFolderRename($any(contextMenuItem()!.item)); closeContextMenu()"
      class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
    >
      <app-svg-icon type="icEdit" class="text-orange-500 w-6 h-6 mr-3"></app-svg-icon>
      <span class="text-light-text dark:text-dark-text">Rename</span>
    </button>
    <button
      (click)="$event.stopPropagation(); onFolderDelete($any(contextMenuItem()!.item)); closeContextMenu()"
      class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
    >
      <app-svg-icon type="icTrash" class="text-red-500 w-6 h-6 mr-3"></app-svg-icon>
      <span class="text-light-text dark:text-dark-text">Delete</span>
    </button>
  </div>
  } @else {
  <!-- File Context Menu -->
  <div
    class="w-[166px] flex flex-col justify-between p-1 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
    (clickOutside)="closeContextMenu()"
  >
    <button
      (click)="$event.stopPropagation(); onFileInfo($any(contextMenuItem()!.item)); closeContextMenu()"
      class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
    >
      <app-svg-icon type="icInfo" class="text-blue-500 w-6 h-6 mr-3"></app-svg-icon>
      <span class="text-light-text dark:text-dark-text">Info</span>
    </button>
    <button
      (click)="$event.stopPropagation(); onFileRename($any(contextMenuItem()!.item)); closeContextMenu()"
      class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
    >
      <app-svg-icon type="icEdit" class="text-orange-500 w-6 h-6 mr-3"></app-svg-icon>
      <span class="text-light-text dark:text-dark-text">Rename</span>
    </button>
    <button
      (click)="$event.stopPropagation(); onFileMove($any(contextMenuItem()!.item)); closeContextMenu()"
      class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
    >
      <svg class="text-blue-500 mr-3" width="24px" height="24px" viewBox="0 0 24 24" focusable="false" fill="#3B82F6">
        <path fill="none" d="M0 0h24v24H0V0z"></path>
        <path d="M20 6h-8l-2-2H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm0 12H4V8h16v10zm-8.01-9l-1.41 1.41L12.16 12H8v2h4.16l-1.59 1.59L11.99 17 16 13.01 11.99 9z"></path>
      </svg>
      <span class="text-light-text dark:text-dark-text">Move</span>
    </button>
    <button
      (click)="$event.stopPropagation(); onFileDelete($any(contextMenuItem()!.item)); closeContextMenu()"
      class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
    >
      <app-svg-icon type="icTrash" class="text-red-500 w-6 h-6 mr-3"></app-svg-icon>
      <span class="text-light-text dark:text-dark-text">Delete</span>
    </button>
  </div>
  }
</div>
}
