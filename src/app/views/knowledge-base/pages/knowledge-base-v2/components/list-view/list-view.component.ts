import { SelectionModel } from '@angular/cdk/collections';
import { CdkConnectedOverlay, CdkOverlayOrigin } from '@angular/cdk/overlay';
import {
  CdkVirtualScrollViewport,
  ScrollingModule,
} from '@angular/cdk/scrolling';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  input,
  OnDestroy,
  output,
  PLATFORM_ID,
  signal,
  Signal,
  ViewChild,
} from '@angular/core';
import { DxTooltip } from '@dx-ui/ui';
import { SvgIconComponent } from '@shared/components';
import { ClickOutsideDirective, LongPressDirective } from '@shared/directives';
import { IFile, IFolder, ISearchModel } from '@shared/models';

@Component({
  selector: 'app-list-view',
  standalone: true,
  imports: [
    CommonModule,
    DxTooltip,
    ScrollingModule,
    SvgIconComponent,
    CdkOverlayOrigin,
    CdkConnectedOverlay,
    ClickOutsideDirective,
    LongPressDirective
  ],
  templateUrl: './list-view.component.html',
  styleUrl: './list-view.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ListViewComponent implements OnDestroy {
  private cdr = inject(ChangeDetectorRef);
  private platformId = inject(PLATFORM_ID);

  // Event listeners for cleanup
  private scrollListener?: () => void;
  private resizeListener?: () => void;

  // Signal-based inputs
  combinedList = input<(IFolder | IFile)[]>([]);
  searchModel = input.required<ISearchModel>();
  folderSelection = input.required<SelectionModel<IFolder>>();
  fileSelection = input.required<SelectionModel<IFile>>();
  isShiftPressed = input.required<Signal<boolean>>();
  isCtrlPressed = input.required<Signal<boolean>>();

  // Signal-based outputs
  folderSelected = output<{ folder: IFolder; event?: MouseEvent }>();
  fileSelected = output<{ file: IFile; event?: MouseEvent }>();
  folderDoubleClick = output<IFolder>();
  fileDoubleClick = output<IFile>();
  folderContextMenu = output<{ folder: IFolder; event: MouseEvent }>();
  fileContextMenu = output<{ file: IFile; event: MouseEvent }>();

  // New specific action outputs
  folderRename = output<any>();
  folderDelete = output<any>();
  fileInfo = output<any>();
  fileRename = output<any>();
  fileMove = output<any>();
  fileDelete = output<any>();
  sortChanged = output<{ order: string; orderBy: string }>();

  @ViewChild('listViewport', { static: false })
  listViewport!: CdkVirtualScrollViewport;

  // Sort state signals
  nameOrder = signal<'ASC' | 'DESC'>('DESC');
  createOrder = signal<'ASC' | 'DESC'>('DESC');
  updateOrder = signal<'ASC' | 'DESC'>('DESC');

  itemSelected = signal<{ id: number; type: 'folder' | 'file' } | undefined>(
    undefined
  );

  // Context menu signals
  contextMenuItem = signal<
    { id: number; type: 'folder' | 'file'; item: IFolder | IFile } | undefined
  >(undefined);
  contextMenuPosition = signal<{ x: number; y: number } | undefined>(undefined);

  constructor() {
    setTimeout(() => {
      // console.log('List view created', this.combinedList());
    }, 1000);

    // Close context menu on scroll or resize
    if (isPlatformBrowser(this.platformId)) {
      this.scrollListener = () => this.closeContextMenu();
      this.resizeListener = () => this.closeContextMenu();

      window.addEventListener('scroll', this.scrollListener, true);
      window.addEventListener('resize', this.resizeListener);
    }
  }

  // Event handlers
  onFolderClick(folder: any, event?: MouseEvent) {
    this.folderSelected.emit({ folder, event });
  }

  onFileClick(file: any, event?: MouseEvent) {
    this.fileSelected.emit({ file, event });
  }

  onFolderDoubleClick(folder: any) {
    this.folderDoubleClick.emit(folder);
  }

  onFileDoubleClick(file: any) {
    this.fileDoubleClick.emit(file);
  }

  onFolderContextMenu(folder: any, event: MouseEvent) {
    this.folderContextMenu.emit({ folder, event });
  }

  onFileContextMenu(file: any, event: MouseEvent) {
    this.fileContextMenu.emit({ file, event });
  }

  // New context menu action methods
  onFolderRename(folder: any) {
    this.folderRename.emit(folder);
  }

  onFolderDelete(folder: any) {
    this.folderDelete.emit(folder);
  }

  onFileInfo(file: any) {
    this.fileInfo.emit(file);
  }

  onFileRename(file: any) {
    this.fileRename.emit(file);
  }

  onFileMove(file: any) {
    this.fileMove.emit(file);
  }

  onFileDelete(file: any) {
    this.fileDelete.emit(file);
  }

  // Sort handlers
  changeFilter(order: 'ASC' | 'DESC', orderBy: string) {
    this.sortChanged.emit({ order, orderBy });

    // Update local sort state
    switch (orderBy) {
      case 'name':
        this.nameOrder.set(order);
        break;
      case 'created_at':
        this.createOrder.set(order);
        break;
      case 'updated_at':
        this.updateOrder.set(order);
        break;
    }
  }

  // Utility methods
  isFolder(item: { isFolder?: boolean } | any): boolean {
    return item && item.isFolder === true;
  }

  isFile(item: { isFolder?: boolean } | any): boolean {
    return item && item.isFolder === false;
  }

  trackByItemId(_index: number, item: IFolder | IFile): number {
    return item.id || 0;
  }

  removeSelectedFolderOrFile() {
    this.folderSelection().clear();
    this.fileSelection().clear();
    this.cdr.detectChanges();
  }

  refreshVirtualScrollViewport() {
    if (this.listViewport) {
      this.listViewport.checkViewportSize();
      this.listViewport.scrollToIndex(0);
    }
    this.cdr.detectChanges();
  }

  getFormattedDate(file: any, type: string): string {
    if (type == 'create') {
      const date = file.created_at;
      return date
        ? new Date(date).toLocaleDateString('en-GB', {
            day: '2-digit',
            month: 'short',
            year: 'numeric',
          })
        : '-';
    } else {
      const date = file.updated_at;
      return date
        ? new Date(date).toLocaleDateString('en-GB', {
            day: '2-digit',
            month: 'short',
            year: 'numeric',
          })
        : '-';
    }
  }

  getFormattedDateTime(file: any, type: string): string {
    let date: Date | null;
    if (type === 'create') {
      date = file.created_at ? new Date(file.created_at) : null;
    } else {
      date = file.updated_at ? new Date(file.updated_at) : null;
    }
    if (!date) return '';
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const month = date.toLocaleString('en-GB', { month: 'short' });
    const year = date.getFullYear();
    return `${hours}:${minutes}:${seconds}, ${day} ${month}, ${year}`;
  }

  getFileIcon(file: IFile): string {
    switch (file.ext) {
      case 'PDF':
        return 'heroDocumentText';
      case 'CSV':
        return 'faSolidFileCsv';
      case 'MD':
        return 'faSolidFilePen';
      case 'TXT':
        return 'faSolidFileLines';
      case 'URL':
        return 'faSolidLink';
      default:
        return 'faSolidFile';
    }
  }

  getFileIconColor(file: IFile): string {
    switch (file.ext) {
      case 'PDF':
        return '#f28b82';
      case 'CSV':
        return '#81c995';
      case 'MD':
        return '#8ab4f8';
      case 'TXT':
        return '#8ab4f8';
      case 'URL':
        return '#c58af9';
      default:
        return '#6F767E';
    }
  }

  openMenuActions(item: IFolder | IFile) {
    const id = item.id || 0;
    const type = item.isFolder === false ? 'file' : 'folder';
    this.itemSelected.set({ id, type });
    // Close context menu when opening regular menu
    this.contextMenuItem.set(undefined);
    this.contextMenuPosition.set(undefined);
  }

  onRightClick(item: IFolder | IFile, event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();

    const id = item.id || 0;
    const type = item.isFolder === false ? 'file' : 'folder';

    // Close regular menu when opening context menu
    this.itemSelected.set(undefined);

    // Calculate menu position to avoid viewport overflow
    const menuWidth = type === 'folder' ? 245 : 166;
    const menuHeight = type === 'folder' ? 80 : 160; // Approximate height

    let x = event.clientX;
    let y = event.clientY;

    // Adjust position if menu would overflow viewport
    if (isPlatformBrowser(this.platformId)) {
      if (x + menuWidth > window.innerWidth) {
        x = window.innerWidth - menuWidth - 10;
      }

      if (y + menuHeight > window.innerHeight) {
        y = window.innerHeight - menuHeight - 10;
      }
    }

    // Set context menu data
    this.contextMenuItem.set({ id, type, item });
    this.contextMenuPosition.set({ x, y });
  }

  closeContextMenu() {
    this.contextMenuItem.set(undefined);
    this.contextMenuPosition.set(undefined);
  }

  closeAllMenus() {
    this.itemSelected.set(undefined);
    this.contextMenuItem.set(undefined);
    this.contextMenuPosition.set(undefined);
  }

  ngOnDestroy() {
    // Cleanup event listeners
    if (isPlatformBrowser(this.platformId)) {
      if (this.scrollListener) {
        window.removeEventListener('scroll', this.scrollListener, true);
      }
      if (this.resizeListener) {
        window.removeEventListener('resize', this.resizeListener);
      }
    }
  }
  getType(item: IFolder | IFile): 'file' | 'folder' {
    return item.isFolder === false ? 'file' : 'folder';
  }
}
