import { Component, computed, inject, OnInit, signal } from '@angular/core';
import {
  Form<PERSON>uilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { UIStore } from '@core/stores';
import {
  DIALOG_DATA,
  DxButton,
  DxDialogRef,
  DxError,
  DxFormField,
  DxInput,
  DxLabel,
  DxLoadingButton,
  DxSnackBar,
} from '@dx-ui/ui';
import { NgIcon, provideIcons } from '@ng-icons/core';
import { heroXMark } from '@ng-icons/heroicons/outline';
import { IToolDev } from '@shared/models';
import { ToolDevService } from '@shared/services';
import { EditorComponent } from 'ngx-monaco-editor-v2';
import {CustomValidators} from '@shared/validators';

@Component({
  selector: 'app-add-tool',
  imports: [
    DxButton,
    DxLoadingButton,
    EditorComponent,
    ReactiveFormsModule,
    NgIcon,
    DxLabel,
    Dx<PERSON>rror,
    DxFormField,
    DxInput,
  ],
  providers: [provideIcons({ heroXMark })],
  templateUrl: './add-tool.component.html',
  styleUrl: './add-tool.component.css',
  host: {
    class: 'h-full',
  },
})
export class AddToolComponent implements OnInit {
  isSubmitting = signal<boolean>(false);

  readonly editorOptions = computed(() => ({
    theme: this.uiStore.theme() === 'dark' ? 'vs-dark' : 'vs-light',
    language: 'json',
    domReadOnly: true,
    automaticLayout: true,
    readOnly: false,
  }));

  formGroup: FormGroup = inject(FormBuilder).group({
    id: [null],
    name: [null,
      [
        Validators.required,
        CustomValidators.patternValidator(/^[a-z][a-z0-9]*(?:_[a-z0-9]+)*$/, { isSnakeCase: true })
      ]
    ],
    description: [null, Validators.required],
    parameters: [null, Validators.required],
    flow_data: [null],
  });
  data: {
    id: number;
    name: string;
    description: string;
    parameters: string;
    flow_data: string;
    isEdit: boolean;
  } = inject(DIALOG_DATA);
  uiStore = inject(UIStore);
  protected dialogRef = inject(DxDialogRef<AddToolComponent>);
  private snackBar = inject(DxSnackBar);
  private toolDevService = inject(ToolDevService);

  ngOnInit(): void {
    if (this.data.isEdit) {
      this.formGroup.patchValue({
        id: this.data.id,
        name: this.data.name,
        description: this.data.description,
        parameters: this.data.parameters,
        flow_data: this.data.flow_data,
      });
    }
  }

  onSave(isEdit: boolean): void {
    if (this.formGroup.invalid) {
      this.formGroup.markAllAsTouched();
      return;
    }

    const body: IToolDev = this.formGroup.value;

    this.isSubmitting.set(true);
    if (isEdit) {
      this.toolDevService.update(body).subscribe({
        next: () => {
          this.snackBar.open('Tool updated successfully!', '', {
            panelClass: 'dx-snack-bar-success',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
          this.dialogRef.close();
          this.isSubmitting.set(false);
        },
        error: () => {
          this.isSubmitting.set(false);
        },
      });
    } else {
      this.toolDevService.insert(body).subscribe({
        next: () => {
          this.snackBar.open('Tool created successfully!', '', {
            panelClass: 'dx-snack-bar-success',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
          this.dialogRef.close();
          this.isSubmitting.set(false);
        },
        error: () => {
          this.isSubmitting.set(false);
        },
      });
    }
  }
}
