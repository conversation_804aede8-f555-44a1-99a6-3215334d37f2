<div class="h-full relative flex flex-col rounded-3xl">
  <!-- Dialog Header -->
  <div
    class="absolute top-0 left-0 right-0 z-2 w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
  >
    <div
      class="flex items-center text-2xl font-bold text-base-content dark:text-dark-base-content"
    >
      {{ data.isEdit ? "Edit Tool" : "Create Tool" }}
    </div>
    <div class="flex items-center justify-end space-x-4">
      <ng-icon
        name="heroXMark"
        class="text-2xl !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
        (click)="dialogRef.close()"
      ></ng-icon>
    </div>
  </div>

  <!-- Dialog Content -->
  <div class="flex-1 overflow-auto mt-18 mb-20">
    <div
      [formGroup]="formGroup"
      class="px-6 pt-6 pb-[3px] flex flex-col space-x-4"
    >
      <dx-form-field class="w-full" id="name">
        <dx-label>Name</dx-label>
        <input
          dx-input
          formControlName="name"
          [type]="'text'"
          placeholder="Name"
        />
        @if (formGroup.get('name')?.errors && formGroup.get('name')?.errors?.['required'] && (formGroup.get('name')?.touched ||
        formGroup.get('name')?.dirty)) {
        <dx-error>Name is required.</dx-error>
        }
        @if (formGroup.get('name')?.errors && formGroup.get('name')?.errors?.['isSnakeCase'] && (formGroup.get('name')?.touched ||
        formGroup.get('name')?.dirty)) {
        <dx-error>Name must be snake_case format (e.g. hello_world).</dx-error>
        }
      </dx-form-field>
      <dx-form-field class="w-full" id="description">
        <dx-label>Description</dx-label>
        <input
          dx-input
          formControlName="description"
          [type]="'text'"
          placeholder="Description"
        />
        @if (formGroup.get('description')?.errors &&
        (formGroup.get('description')?.touched ||
        formGroup.get('description')?.dirty)) {
        <dx-error>Description is required.</dx-error>
        }
      </dx-form-field>
      <dx-form-field
        class="w-full"
        id="parameters"
        [style.--dx-form-field-outlined-input-background]="
          uiStore.theme() === 'dark' ? '#1E1E1EFF' : 'white'
        "
      >
        <dx-label>Parameters <span class="text-error">*</span></dx-label>
        <ngx-monaco-editor
          class="h-full"
          [options]="editorOptions()"
          formControlName="parameters"
        ></ngx-monaco-editor>
        @if (formGroup.get('parameters')?.errors &&
        (formGroup.get('parameters')?.touched ||
        formGroup.get('parameters')?.dirty)) {
        <dx-error>Parameters is required.</dx-error>
        }
      </dx-form-field>
    </div>
  </div>

  <!-- Dialog Footer -->
  <div
    class="absolute left-0 bottom-0 right-0 z-2 flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <button dxButton="elevated" (click)="dialogRef.close()">
      <span>Cancel</span>
    </button>
    <button
      dxLoadingButton="filled"
      [id]="'btn-submit-tool-form'"
      [loading]="isSubmitting()"
      [disabled]="formGroup.invalid"
      (click)="onSave(data.isEdit)"
    >
      {{ data.isEdit ? "Update" : "Create" }}
    </button>
  </div>
</div>
