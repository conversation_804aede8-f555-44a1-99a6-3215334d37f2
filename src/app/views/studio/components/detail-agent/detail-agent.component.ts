import {Component, computed, inject, OnInit, signal} from '@angular/core';
import {DIALOG_DATA, DxButton, DxDialogRef, DxFormField, DxInput, DxLabel, DxTooltip} from '@dx-ui/ui';
import {IAgent, IAgentConfig, IAgentDev, IAgentSetting} from '@shared/models';
import {NgIcon, provideIcons} from '@ng-icons/core';
import {heroXMark} from '@ng-icons/heroicons/outline';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {AutosizeDirective} from '@shared/directives';
import { SettingsService } from '@shared/services';
import {SvgIconComponent} from '@shared/components';

@Component({
  selector: 'app-detail-agent',
  imports: [
    NgIcon,
    DxButton,
    ReactiveFormsModule,
    DxInput,
    DxLabel,
    DxFormField,
    FormsModule,
    AutosizeDirective,
    SvgIconComponent,
    DxTooltip
  ],
  providers:[provideIcons({heroXMark})],
  templateUrl: './detail-agent.component.html',
  styleUrl: './detail-agent.component.css',
  host: {
    class:'h-full'
  }
})
export class DetailAgentComponent implements OnInit {
  dialogRef = inject(DxDialogRef<DetailAgentComponent>);
  data: {
    agent: IAgentDev | IAgent
  } = inject(DIALOG_DATA);
  settingAgentData = signal<IAgentSetting|undefined>(undefined);

  dataRAGAgent = computed(() => {
    return this.settingAgentData()?.rag;
  });

  settingsService = inject(SettingsService);
  modelAi = '';
  ngOnInit(): void {
    const modelCfg = JSON.parse(this.data.agent.model_config);
    this.modelAi = modelCfg.name;
    if (this.isRAGAgent(this.data.agent)) {
      this.getRAGSetting();
    }
  }

  getRAGSetting(){
    this.settingsService.getDetailSetting().subscribe({
      next: (data: IAgentConfig) => {
        if (data) {
          this.settingAgentData.set(data.settings?.agent_setting);
        }
      },
    });
  }

  isRAGAgent(agent: IAgentDev) {
    return agent.name.toLowerCase().includes(' rag ');
  }

  isDefaultAgent(agent: IAgentDev) {
    return agent.is_default;
  }
}
