import { Component, inject, OnInit, signal } from '@angular/core';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  DIALOG_DATA,
  DxButton,
  DxDialogRef,
  DxError,
  DxFormField,
  DxInput,
  DxLabel,
  DxLoadingButton,
  DxOption,
  DxSelect,
  DxSnackBar,
  DxTooltip,
} from '@dx-ui/ui';
import { NgIcon, NgIconsModule, provideIcons } from '@ng-icons/core';
import { heroInformationCircle, heroXMark } from '@ng-icons/heroicons/outline';
import { SvgIconComponent } from '@shared/components';
import { AutosizeDirective } from '@shared/directives';
import { IAgentConfig, IAgentDev } from '@shared/models';
import { AgentDevService, SettingsService } from '@shared/services';

@Component({
  selector: 'app-add-or-edit-agent',
  imports: [
    NgIcon,
    ReactiveFormsModule,
    DxButton,
    DxLoadingButton,
    DxError,
    DxLabel,
    DxFormField,
    DxInput,
    DxSelect,
    DxOption,
    AutosizeDirective,
    SvgIconComponent,
    DxTooltip,
    NgIconsModule,
  ],
  providers: [provideIcons({ heroXMark, heroInformationCircle })],
  templateUrl: './add-or-edit-agent.component.html',
  styleUrl: './add-or-edit-agent.component.css',
  host: {
    class: 'h-full',
  },
})
export class AddOrEditAgentComponent implements OnInit {
  isSubmitting = signal<boolean>(false);
  settingData = signal<IAgentConfig>({});

  aiModels = signal<any[]>([
    { value: 'gpt-4o-mini', type: 'openai', label: 'gpt-4o-mini' },
    { value: 'gpt-4o', type: 'openai', label: 'gpt-4o' },
    { value: 'gpt-4.1-mini', type: 'openai', label: 'gpt-4.1-mini' },
    { value: 'gpt-4.1-nano', type: 'openai', label: 'gpt-4.1-nano' },
    { value: 'gpt-4.1', type: 'openai', label: 'gpt-4.1' },
    { value: 'gemini-1.5-flash', type: 'gemini', label: 'gemini-1.5-flash' },
    { value: 'gemini-2.0-flash', type: 'gemini', label: 'gemini-2.0-flash' },
  ]);

  modelConfig: { name: string; temperature: number } = {
    name: 'openai',
    temperature: 0,
  };

  formGroup: FormGroup = inject(FormBuilder).group({
    id: [null],
    ai_id: [null],
    name: [null, Validators.required],
    description: [null, Validators.required],
    llm_type: [this.aiModels()[0].type, Validators.required],
    instruction: [null, Validators.required],
    model_config: [this.modelConfig],
    rule: [null],
    role: [null],
    temperature: [
      0,
      [Validators.required, Validators.min(0), Validators.max(1)],
    ],
    selectedModel: [this.aiModels()[0].value, Validators.required],
  });
  dialogRef = inject(DxDialogRef<AddOrEditAgentComponent>);
  snackBar = inject(DxSnackBar);
  data: {
    agent: IAgentDev;
    model_config: string;
    temperature: number;
    selectedModel: any;
    isEdit: boolean;
  } = inject(DIALOG_DATA);
  private agentDevService = inject(AgentDevService);
  private settingsService = inject(SettingsService);
  ngOnInit() {
    this.formGroup.get('selectedModel')?.valueChanges.subscribe((value) => {
      if (value) {
        const selectedModel = this.aiModels().find(
          (model) => model.value === value
        );
        if (selectedModel) {
          this.formGroup.get('llm_type')?.setValue(selectedModel.type);
          this.modelConfig.name = selectedModel.value;
        }
      }
    });
    if (this.data.isEdit) {
      if (this.isRAGAgent(this.data.agent)) {
        Object.keys(this.formGroup.controls).forEach((key) => {
          this.formGroup.get(key)?.clearValidators();
          this.formGroup.get(key)?.updateValueAndValidity();
        });
        this.settingsService.getDetailSetting().subscribe({
          next: (data: IAgentConfig) => {
            if (data) {
              this.settingData.set(data);
              const dataAgent = data.settings?.agent_setting?.rag;
              this.formGroup.patchValue({
                name: this.data.agent.name,
                description: dataAgent?.description,
                role: dataAgent?.role,
                rule: dataAgent?.rule,
              });
            }
          },
        });
      } else {
        this.formGroup.patchValue({
          id: this.data.agent.id,
          ai_id: this.data.agent.ai_id,
          name: this.data.agent.name,
          description: this.data.agent.description,
          llm_type: this.data.agent.llm_type,
          instruction: this.data.agent.instruction,
          model_config: this.data.model_config,
          rule: this.data.agent.rule,
          temperature: this.data.temperature,
          selectedModel: this.data.selectedModel,
          role: this.data.agent.role,
        });
      }
    } else {
      this.formGroup.patchValue({
        llm_type: this.aiModels()[0].type,
        temperature: 0,
        selectedModel: this.aiModels()[0].value,
      });
      this.modelConfig = {
        name: this.aiModels()[0].value,
        temperature: 0,
      };
    }
    // Update modelConfig.temperature when form temperature changes
    this.formGroup.get('temperature')?.valueChanges.subscribe((value) => {
      if (value !== null && value !== undefined) {
        this.modelConfig.temperature = value;
      }
    });
  }

  onSave(isEdit: boolean): void {
    if (this.formGroup.invalid) {
      this.formGroup.markAllAsTouched();
      return;
    }
    let body !: IAgentDev;
    if (isEdit){
      body = {
        ...this.data.agent,
        ...this.formGroup.value,
      }
    } else {
      body = this.formGroup.value;
    }
    body.model_config = JSON.stringify(this.modelConfig);

    this.isSubmitting.set(true);
    if (isEdit) {
      if (this.isRAGAgent(this.data.agent)) {
        const currentSettings = this.settingData().settings;
        const updatedSettings = {
          ...currentSettings,
          agent_setting: {
            ...currentSettings?.agent_setting,
            rag: {
              ...currentSettings?.agent_setting?.rag,
              description: this.formGroup.get('description')?.value,
              role: this.formGroup.get('role')?.value,
              rule: this.formGroup.get('rule')?.value,
            },
          },
        };
        this.settingsService.updateSetting(updatedSettings).subscribe({
          next: () => {
            this.isSubmitting.set(false);
            this.dialogRef.close();
            this.snackBar.open('Agent updated successfully!', '', {
              panelClass: 'dx-snack-bar-success',
              duration: 5000,
              verticalPosition: 'top',
              horizontalPosition: 'right',
            });
          },
          error: () => {
            this.isSubmitting.set(false);
          },
        });
      } else {
        this.agentDevService.update(body).subscribe({
          next: () => {
            this.isSubmitting.set(false);
            this.dialogRef.close();
            this.snackBar.open('Agent updated successfully!', '', {
              panelClass: 'dx-snack-bar-success',
              duration: 5000,
              verticalPosition: 'top',
              horizontalPosition: 'right',
            });
          },
          error: () => {
            this.isSubmitting.set(false);
          },
        });
      }
    } else {
      this.agentDevService.insert(body).subscribe({
        next: () => {
          this.isSubmitting.set(false);
          this.dialogRef.close();
          this.snackBar.open('Agent created successfully!', '', {
            panelClass: 'dx-snack-bar-success',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
        },
        error: (error) => {
          this.isSubmitting.set(false);
        },
      });
    }
  }
  isRAGAgent(agent: IAgentDev) {
    return (
      this.data &&
      this.data.isEdit &&
      agent.name.toLowerCase().includes(' rag ')
    );
  }
}
