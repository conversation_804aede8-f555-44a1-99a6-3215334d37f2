@if (!uiStore.isHandset()) {
  <div class="h-full flex flex-col overflow-hidden">
    <div class="flex items-start justify-between">
      <h1
        class="text-xl font-semibold text-neutral-content dark:text-dark-neutral-content"
      >
        Agent Management
      </h1>
      <button
        dxButton="elevated"
        (click)="navigateToFlowEvent()"
        class="px-4"
      >
        <div class="flex items-center justify-between space-x-2">
          <span>Flow Event</span>
          <app-svg-icon
            type="icChevronDoubleForward"
            class="w-5 h-5"
          ></app-svg-icon>
        </div>
      </button>
    </div>

    <div class="mt-4 h-full flex flex-col w-full">
      <div class="w-full h-full grid grid-cols-8 gap-x-4">
        <div class="col-span-3 grid grid-cols-7 gap-x-4">
          <div
            class="col-span-6 h-full flex flex-col w-full p-6 space-y-6 rounded-2xl bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border"
          >
            <!--          header-->
            <div class="flex flex-col space-y-4 justify-between">
              <div
                class="text-2xl font-bold text-base-content dark:text-dark-base-content"
              >
                Tools
              </div>
              <div class="flex space-x-4 items-center justify-between">
                <dx-form-field
                  class="flex items-center justify-start"
                  [style.margin-bottom]="0"
                  [style.--dx-form-field-label-offset-y]="0"
                  [subscriptHidden]="true"
                >
                  <input
                    dxInput
                    [type]="'text'"
                    [ngModel]="searchToolTerm()"
                    (ngModelChange)="searchToolTerm.set($event)"
                    placeholder="Search by Name"
                  />
                </dx-form-field>
                @if (studioStore.status() === STUDIO_STATUS.DEV) {
                  <div class="flex items-center justify-end">
                    <button
                      dxButton="filled"
                      (click)="openCreateToolDialog()"
                      class="px-4"
                    >
                      <div class="flex items-center justify-between space-x-1">
                        <app-svg-icon
                          type="icPlus"
                          class="w-6 h-6 text-white"
                        ></app-svg-icon>
                        <span class="text-sm font-medium">Add Tool</span>
                      </div>
                    </button>
                  </div>
                }
              </div>
            </div>
            <!--          content-->
            <div class="h-full">
              <div
                cdkDropList
                [cdkDropListData]="filteredTools()"
                [cdkDropListConnectedTo]="connectedDropLists()"
                id="tool-source"
                class="h-full overflow-auto list-tools w-full space-y-4"
              >
                @for (tool of filteredTools(); track tool.id) {
                  <div
                    cdkDrag
                    [cdkDragData]="tool"
                    (cdkDragStarted)="onDragStarted($event)"
                    (cdkDragEnded)="onDragEnded($event)"
                    class="rounded-2xl flex flex-col border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400 cursor-grab"
                  >
                    <div class="px-6 py-5 flex flex-col relative">
                      <div
                        class="flex space-x-3 !text-base-content dark:!text-dark-base-content"
                      >
                        <app-svg-icon
                          type="icTool"
                          class="flex-none w-6 h-6 justify-start"
                        ></app-svg-icon>
                        <div
                          class="flex-1 grow truncate text-base font-bold items-end !cursor-pointer hover:!underline"
                          (click)="onEditFlowTool(tool)"
                        >
                          {{ tool.name }}
                        </div>
                        @if (studioStore.status() === STUDIO_STATUS.DEV) {
                          <app-svg-icon
                            type="icMoreHorizontal"
                            class="flex-none w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
                            cdkOverlayOrigin
                            #trigger="cdkOverlayOrigin"
                            (click)="
                        openToolAction.set(
                          openToolAction() ? undefined : tool.id
                        );
                        $event.stopPropagation()
                      "
                          >
                          </app-svg-icon>
                          <ng-template
                            cdkConnectedOverlay
                            [cdkConnectedOverlayOrigin]="trigger"
                            [cdkConnectedOverlayOpen]="openToolAction() === tool.id"
                            [cdkConnectedOverlayPush]="true"
                            [cdkConnectedOverlayPositions]="[
                                  {
                                    originX: 'start',
                                    originY: 'center',
                                    overlayX: 'end',
                                    overlayY: 'top',
                                    offsetY: 10,
                                  },
                                  {
                                    originX: 'start',
                                    originY: 'center',
                                    overlayX: 'end',
                                    overlayY: 'bottom',
                                    offsetY: 10
                                  },
                                ]"
                          >
                            <ul
                              class="w-[245px] flex flex-col justify-between p-1 z-50 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
                              (clickOutside)="openToolAction.set(undefined)"
                            >
                              <li
                                (click)="$event.stopPropagation(); onEditTool(tool)"
                                class="flex w-full px-3 py-[10px] gap-2.5 text-[16px] font-medium items-center cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                              >
                                <app-svg-icon
                                  type="icEdit"
                                  class="w-6 h-6"
                                ></app-svg-icon>
                                Edit
                              </li>
                              <li
                                (click)="$event.stopPropagation(); onDeleteTool(tool)"
                                class="flex w-full px-3 py-[10px] gap-2.5 text-[16px] font-medium items-center cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                              >
                                <app-svg-icon
                                  type="icTrash"
                                  class="w-6 h-6"
                                ></app-svg-icon>
                                Delete
                              </li>
                            </ul>
                          </ng-template>
                        }
                      </div>
                      <div
                        class="mt-2 ml-9 text-neutral-content dark:text-neutral-content text-[13px] font-normal"
                      >
                        {{ tool.description }}
                      </div>
                    </div>
                  </div>
                } @empty {
                  <div
                    class="text-neutral-content dark:text-dark-neutral-content flex items-center justify-center text-sm py-4"
                  >
                    <span>No data available</span>
                  </div>
                }
              </div>
            </div>
          </div>
          <div class="col-span-1 flex items-center justify-center">
            <div class="w-16 h-16 flex items-center justify-center p-[11px]">
              <app-svg-icon
                type="icArrowRight"
                class="w-10.5 h-10.5 !text-neutral-content dark:!text-dark-neutral-content"
              ></app-svg-icon>
            </div>
          </div>
        </div>

        <div
          class="col-span-5 h-full flex flex-col w-full p-6 space-y-6 rounded-2xl bg-base-200 dark:bg-dark-base-200 border border-primary-border dark:border-dark-primary-border"
        >
          <div class="flex flex-col space-y-4 justify-between">
            <div
              class="text-2xl font-bold text-base-content dark:text-dark-base-content"
            >
              Agents
            </div>
            <div class="flex space-x-4 items-center justify-between">
              <dx-form-field
                class="flex md:w-[376px] items-center justify-start"
                [style.margin-bottom]="0"
                [style.--dx-form-field-label-offset-y]="0"
                [subscriptHidden]="true"
              >
                <input
                  dxInput
                  [type]="'text'"
                  [ngModel]="searchAgentTerm()"
                  (ngModelChange)="searchAgentTerm.set($event)"
                  placeholder="Search by Name"
                />
              </dx-form-field>
              @if (studioStore.status() === STUDIO_STATUS.DEV) {
                <div class="flex items-center justify-end">
                  <button
                    dxButton="filled"
                    (click)="openCreateAgentDialog()"
                    class="px-4"
                  >
                    <div class="flex items-center justify-between space-x-1">
                      <app-svg-icon
                        type="icPlus"
                        class="w-6 h-6 text-white"
                      ></app-svg-icon>
                      <span class="text-sm">Add Agent</span>
                    </div>
                  </button>
                </div>
              }
            </div>
          </div>

          <div class="flex-1 flex flex-col overflow-y-auto list-agents">
            @for (agent of filteredAgents(); track agent.id) {
              <div class="mb-4 relative">
                <div
                  [id]="'agent-drop-' + agent.id"
                  cdkDropList
                  [cdkDropListData]="agent.tools || []"
                  [cdkDropListConnectedTo]="connectedDropLists()"
                  [cdkDropListEnterPredicate]="getEnterPredicate(agent)"
                  (cdkDropListDropped)="onDrop($event, agent.id || 0)"
                  class="rounded-2xl flex flex-col border border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
                >
                  <div
                    class="rounded-2xl px-6 py-5 flex items-center justify-between bg-base-400 dark:bg-dark-base-400"
                    [ngClass]="{
                  'outline outline-primary dark:outline-primary':
                    selectingAgentId() === agent.id
                }"
                  >
                    <div class="flex items-center space-x-4">
                      <div class="w-fit py-1 flex justify-between items-center">
                        <div
                          class="flex-none flex rounded-full w-10 h-10 px-[10px] pt-[8px] pb-[12px] bg-primary justify-between items-center"
                        >
                          <app-svg-icon type="icUnion" class="w-5 h-5 rounded-full !text-white"></app-svg-icon>
                        </div>
                      </div>
                      <div class="grow flex flex-col space-x-2">
                        <div
                          class="text-base-content dark:text-dark-base-content text-xl font-bold truncate max-w-[30dvw]"
                        >
                          {{ agent.name }}
                        </div>
                        <div
                          class="text-neutral-content dark:text-dark-neutral-content text-[15px] font-normal"
                        >
                          {{ agent.description }}
                        </div>
                      </div>
                    </div>
                    <div class="flex-none flex justify-end items-center space-x-2">
                      @if (!isRAGAgent(agent) && !agent.is_default) {
                        <div
                          class="text-[15px] font-semibold whitespace-nowrap text-primary dark:text-dark-primary cursor-pointer"
                          (click)="selectAgent(agent.id)"
                        >
                          {{ agent.tools?.length || 0 }} tools
                        </div>
                        <app-svg-icon
                          [type]="
                      agent.id && agent.id === selectingAgentId()
                        ? 'icChevronUp'
                        : 'icChevronDown'
                    "
                          class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
                          (click)="selectAgent(agent.id)"
                        ></app-svg-icon>
                      }
                      <app-svg-icon
                        type="icMoreHorizontal"
                        class="ml-4 w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
                        (click)="
                        $event.stopPropagation();
                        openAgentAction.set(
                          openAgentAction() ? undefined : agent.id
                      )
                    "
                        cdkOverlayOrigin
                        #trigger="cdkOverlayOrigin"
                      ></app-svg-icon>
                      <ng-template
                        cdkConnectedOverlay
                        [cdkConnectedOverlayOrigin]="trigger"
                        [cdkConnectedOverlayOpen]="openAgentAction() === agent.id"
                        [cdkConnectedOverlayPush]="true"
                        [cdkConnectedOverlayPositions]="[
                        {
                          originX: 'start',
                          originY: 'center',
                          overlayX: 'end',
                          overlayY: 'top',
                          offsetY: 10,
                        },
                        {
                          originX: 'start',
                          originY: 'center',
                          overlayX: 'end',
                          overlayY: 'bottom',
                          offsetY: 10
                        },
                      ]"
                      >
                        <ul
                          class="w-[245px] flex flex-col justify-between p-1 z-50 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
                          (clickOutside)="openAgentAction.set(undefined)"
                        >
                          <li
                            (click)="$event.stopPropagation(); onViewAgent(agent)"
                            class="flex w-full px-3 py-[10px] gap-2.5 text-[16px] font-medium items-center cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                          >
                            <app-svg-icon type="icShow" class="w-6 h-6"></app-svg-icon>
                            View
                          </li>
                          <li
                            (click)="$event.stopPropagation(); onEditAgent(agent)"
                            class="flex w-full px-3 py-[10px] gap-2.5 text-[16px] font-medium items-center cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                          >
                            <app-svg-icon
                              type="icEdit"
                              class="w-6 h-6"
                            ></app-svg-icon>
                            Edit
                          </li>
                          @if (!isRAGAgent(agent) && !agent.is_default) {
                            <li (click)="$event.stopPropagation(); onDeleteAgent(agent)"
                                class="flex w-full px-3 py-[10px] gap-2.5 text-[16px] font-medium items-center cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                            >
                              <app-svg-icon type="icTrash" class="w-6 h-6"></app-svg-icon>
                              Delete
                            </li>
                          }
                        </ul>
                      </ng-template>
                    </div>
                  </div>
                  @if (agent.id && agent.id === selectingAgentId()) {
                    <div>
                      <div
                        cdkDropList
                        [id]="'agent-tools-' + agent.id"
                        [cdkDropListData]="agent.tools || []"
                        [cdkDropListConnectedTo]="connectedDropLists()"
                        (cdkDropListDropped)="onDrop($event, agent.id!)"
                        [cdkDropListEnterPredicate]="getEnterPredicate(agent)"
                        (click)="$event.stopPropagation()"
                        class="pr-6 pl-[29px] py-4 min-h-[100px] rounded-lg drop-area overflow-y-auto space-y-4"
                      >
                        @if (agent.tools && agent.tools.length > 0) {
                          @for (tool of agent.tools; track tool.id) {
                            <div
                              cdkDrag
                              [cdkDragData]="tool"
                              (cdkDragStarted)="onDragStarted($event)"
                              (cdkDragEnded)="onDragEnded($event)"
                              class="flex justify-between items-center cursor-move agent-tool-item"
                            >
                              <div class="w-full flex flex-row items-center space-x-2">
                                <app-svg-icon
                                  type="icDrag"
                                  class="w-6 h-6 flex-none !text-neutral-content dark:!text-dark-neutral-content"
                                ></app-svg-icon>
                                <div
                                  class="w-full px-6 truncate py-4 grow flex-1 flex items-center justify-between rounded-2xl space-x-3 border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400 !text-base-content dark:!text-dark-base-content"
                                >
                                    <app-svg-icon
                                      type="icTool"
                                      class="flex-none w-6 h-6"
                                    ></app-svg-icon>
                                    <div
                                      class="flex-1 grow truncate text-base font-bold"
                                    >
                                      {{ tool.name }}
                                    </div>
                                  <div class="flex-none justify-end flex space-x-4">
                                    @if (studioStore.status() === STUDIO_STATUS.DEV) {
                                      <app-svg-icon
                                        type="icClose"
                                        class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
                                        (click)="
                                        removeToolFromAgent(agent.id || 0,tool.id || 0);
                                        $event.stopPropagation()"
                                      ></app-svg-icon>
                                      <app-svg-icon
                                        type="icMoreHorizontal"
                                        class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
                                        cdkOverlayOrigin
                                        #trigger="cdkOverlayOrigin"
                                        (click)="
                                          openToolAgentAction.set(openToolAgentAction() ? undefined : tool.id);
                                          $event.stopPropagation()"
                                      >
                                      </app-svg-icon>
                                      <ng-template
                                        cdkConnectedOverlay
                                        [cdkConnectedOverlayOrigin]="trigger"
                                        [cdkConnectedOverlayOpen]="openToolAgentAction() === tool.id"
                                        [cdkConnectedOverlayPush]="true"
                                        [cdkConnectedOverlayPositions]="[
                                              {
                                                originX: 'start',
                                                originY: 'center',
                                                overlayX: 'end',
                                                overlayY: 'top',
                                                offsetY: 10,
                                              },
                                              {
                                                originX: 'start',
                                                originY: 'center',
                                                overlayX: 'end',
                                                overlayY: 'bottom',
                                                offsetY: 10
                                              },
                                            ]"
                                      >
                                        <ul
                                          class="w-[245px] flex flex-col justify-between p-1 z-50 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
                                          (clickOutside)="
                                openToolAgentAction.set(undefined)
                              "
                                        >
                                          <li
                                            (click)="
                                  $event.stopPropagation(); onEditTool(tool)
                                "
                                            class="flex w-full px-3 py-[10px] gap-2.5 text-[16px] font-medium items-center cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                                          >
                                            <app-svg-icon
                                              type="icEdit"
                                              class="w-6 h-6"
                                            ></app-svg-icon>
                                            Edit
                                          </li>
                                          <li
                                            (click)="
                                  $event.stopPropagation(); onDeleteTool(tool)
                                "
                                            class="flex w-full px-3 py-[10px] gap-2.5 text-[16px] font-medium items-center cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                                          >
                                            <app-svg-icon
                                              type="icTrash"
                                              class="w-6 h-6"
                                            ></app-svg-icon>
                                            Delete
                                          </li>
                                        </ul>
                                      </ng-template>
                                    }
                                  </div>
                                </div>
                              </div>
                            </div>
                          }
                        } @else {
                          <div
                            class="text-center text-light-text/50 dark:text-dark-text/50 py-4"
                          >
                            No data available
                          </div>
                        }
                      </div>
                    </div>
                  }
                </div>
              </div>
            }
          </div>
        </div>
      </div>
    </div>
  </div>
} @else {
  <app-mobile-header [title]="title" [backFn]="backFn"></app-mobile-header>
  <div
    class="w-full h-[100dvh] flex flex-col gap-y-3 pt-18 px-4 pb-4 overflow-hidden"
  >
    <div class="flex items-center justify-between space-x-3">
      <!--Search -->
      <dx-form-field
        class="flex flex-1 items-center justify-start"
        [style.margin-bottom]="0"
        [style.--dx-form-field-label-offset-y]="0"
        [subscriptHidden]="true"
      >
        <app-svg-icon
          dxPrefix
          type="icSearch"
          class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
        ></app-svg-icon>
        <input
          dxInput
          [type]="'text'"
          [ngModel]="searchAgentTerm()"
          (ngModelChange)="searchAgentTerm.set($event)"
          placeholder="Search by Name"
        />
      </dx-form-field>
      @if (studioStore.status() === STUDIO_STATUS.DEV) {
        <div class="flex-shrink-0 flex items-center space-x-1">
          <div
            class="w-10 h-10 rounded-full flex items-center justify-center bg-white dark:bg-black border border-primary-border dark:border-dark-primary-border"
            (click)="openCreateAgentDialog()"
          >
            <app-svg-icon
              type="icPlus"
              class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
            ></app-svg-icon>
          </div>
        </div>
      }
    </div>
    <div class="flex-1 flex flex-col overflow-y-auto">
      @for (agent of filteredAgents(); track agent.id) {
        <div class="mb-4 relative">
          <div
            class="rounded-2xl flex flex-col border border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
          >
            <div
              class="rounded-2xl px-4 py-3 bg-base-400 dark:bg-dark-base-400"
              [ngClass]="{
            'outline outline-primary dark:outline-primary':
              selectingAgentId() === agent.id
          }"
              (click)="selectAgent(agent.id)"
            >
              <div class="flex items-center justify-between space-x-4 relative">
                <div class="flex items-center space-x-3">
                  <div class="w-fit py-1 flex justify-between items-center">
                    <div
                      class="rounded-full w-9 h-9 flex bg-primary justify-center items-center"
                    >
                      <app-svg-icon
                        type="icUnion"
                        class="w-5 h-5 rounded-full !text-white mb-1"
                      ></app-svg-icon>
                    </div>
                  </div>
                  <div class="flex flex-col space-x-2">
                    <div
                      class="text-base-content dark:text-dark-base-content text-md font-bold line-clamp-1"
                    >
                      {{ agent.name }}
                    </div>
                  </div>
                </div>
                <div class="flex justify-end items-center space-x-1">
                  <div
                    class="text-[14px] font-semibold whitespace-nowrap text-primary dark:text-dark-primary cursor-pointer"
                  >
                    {{ agent.tools?.length || 0 }} tools
                  </div>
                  <app-svg-icon
                    [type]="
                  agent.id && agent.id === selectingAgentId()
                    ? 'icChevronUp'
                    : 'icChevronDown'
                "
                    class="w-5 h-5 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
                  ></app-svg-icon>
                  <app-svg-icon
                    type="icMoreHorizontal"
                    class="ml-2 w-5 h-5 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
                    (click)="
                  $event.stopPropagation();
                  openAgentAction.set(openAgentAction() ? undefined : agent.id)
                "
                    cdkOverlayOrigin
                    #trigger="cdkOverlayOrigin"
                  ></app-svg-icon>
                  <ng-template
                    cdkConnectedOverlay
                    [cdkConnectedOverlayOrigin]="trigger"
                    [cdkConnectedOverlayOpen]="openAgentAction() === agent.id"
                    [cdkConnectedOverlayPush]="true"
                    [cdkConnectedOverlayPositions]="[
                        {
                          originX: 'start',
                          originY: 'center',
                          overlayX: 'end',
                          overlayY: 'top',
                          offsetY: 10,
                        },
                        {
                          originX: 'start',
                          originY: 'center',
                          overlayX: 'end',
                          overlayY: 'bottom',
                          offsetY: 10
                        },
                      ]"
                  >
                    <ul
                      class="w-[245px] flex flex-col justify-between p-1 z-50 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
                      (clickOutside)="openAgentAction.set(undefined)"
                    >
                      <li
                        (click)="$event.stopPropagation(); onViewAgent(agent)"
                        class="flex w-full px-3 py-[10px] gap-2.5 text-[16px] font-medium items-center cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                      >
                        <app-svg-icon
                          type="icShow"
                          class="w-6 h-6 flex"
                        ></app-svg-icon>
                        View
                      </li>
                      <li
                        (click)="$event.stopPropagation(); onEditAgent(agent)"
                        class="flex w-full px-3 py-[10px] gap-2.5 text-[16px] font-medium items-center cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                      >
                        <app-svg-icon type="icEdit" class="w-6 h-6"></app-svg-icon>
                        Edit
                      </li>
                      <li
                        (click)="$event.stopPropagation(); onDeleteAgent(agent)"
                        class="flex w-full px-3 py-[10px] gap-2.5 text-[16px] font-medium items-center cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                      >
                        <app-svg-icon type="icTrash" class="w-6 h-6"></app-svg-icon>
                        Delete
                      </li>
                    </ul>
                  </ng-template>
                </div>
              </div>
              <div
                class="ml-12 mr-3 text-neutral-content dark:text-dark-neutral-content text-[15px] font-normal"
                [ngClass]="{
              'truncate max-w-[50dvw]': agent.id !== selectingAgentId()
            }"
              >
                {{ agent.description }}
              </div>
            </div>

            @if (agent.id && agent.id === selectingAgentId()) {
              <div>
                <div
                  (click)="$event.stopPropagation()"
                  class="p-3 pl-9 lg:pr-6 lg:pl-[29px] min-h-[100px] flex flex-col rounded-lg py-4 overflow-y-auto space-y-2"
                >
                  @if (agent.tools && agent.tools.length > 0) {
                    @for (tool of
                      agent.tools; track tool.id) {
                      <div
                        class="flex justify-between items-center cursor-move agent-tool-item"
                      >
                        <div class="space-x-2 w-full">
                          <div class="flex flex-row items-center space-x-2">
                            <div
                              class="p-3 lg:px-6 lg:py-4 w-full flex space-x-3 rounded-2xl border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
                            >
                              <app-svg-icon
                                type="icTool"
                                class="w-6 h-6 !text-base-content dark:!text-dark-base-content flex-none justify-start"
                              ></app-svg-icon>
                              <div
                                class="flex-1 grow truncate text-base-content dark:text-dark-base-content text-base font-bold"
                              >
                                {{ tool.name }}
                              </div>
                              <div class="flex-none flex justify-end space-x-4">
                                @if (studioStore.status() === STUDIO_STATUS.DEV) {
                                  <app-svg-icon
                                    type="icClose"
                                    class="w-5 h-5 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
                                    (click)="
                          removeToolFromAgent(agent.id || 0, tool.id || 0);
                          $event.stopPropagation()
                        "
                                  ></app-svg-icon>
                                  <app-svg-icon
                                    type="icMoreHorizontal"
                                    class="w-5 h-5 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
                                    cdkOverlayOrigin
                                    #trigger="cdkOverlayOrigin"
                                    (click)="
                          openToolAgentAction.set(
                            openToolAgentAction() ? undefined : tool.id
                          );
                          $event.stopPropagation()
                        "
                                  >
                                  </app-svg-icon>
                                  <ng-template
                                    cdkConnectedOverlay
                                    [cdkConnectedOverlayOrigin]="trigger"
                                    [cdkConnectedOverlayOpen]="
                          openToolAgentAction() === tool.id
                        "
                                    [cdkConnectedOverlayPush]="true"
                                    [cdkConnectedOverlayPositions]="[
                                    {
                                      originX: 'start',
                                      originY: 'center',
                                      overlayX: 'end',
                                      overlayY: 'top',
                                      offsetY: 10,
                                    },
                                    {
                                      originX: 'start',
                                      originY: 'center',
                                      overlayX: 'end',
                                      overlayY: 'bottom',
                                      offsetY: 10
                                    },
                                  ]"
                                  >
                                    <ul
                                      class="w-[245px] flex flex-col justify-between p-1 z-50 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
                                      (clickOutside)="openToolAgentAction.set(undefined)"
                                    >
                                      <li
                                        (click)="$event.stopPropagation(); onEditTool(tool)"
                                        class="flex w-full px-3 py-[10px] gap-2.5 text-[16px] font-medium items-center cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                                      >
                                        <app-svg-icon
                                          type="icEdit"
                                          class="w-6 h-6"
                                        ></app-svg-icon>
                                        Edit
                                      </li>
                                      <li
                                        (click)="
                              $event.stopPropagation(); onDeleteTool(tool)
                            "
                                        class="flex w-full px-3 py-[10px] gap-2.5 text-[16px] font-medium items-center cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                                      >
                                        <app-svg-icon
                                          type="icTrash"
                                          class="w-6 h-6"
                                        ></app-svg-icon>
                                        Delete
                                      </li>
                                    </ul>
                                  </ng-template>
                                }
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    }
                  } @else {
                    <div
                      class="text-center text-light-text/50 dark:text-dark-text/50 py-4"
                    >
                      No data available
                    </div>
                  }
                  <div
                    (click)="viewListTool.set(true)"
                    class="space-x-2 !text-primary dark:!text-dark-primary cursor-pointer flex items-center"
                  >
                    <app-svg-icon type="icPlus" class="w-6 h-6"></app-svg-icon>
                    <span class="text-sm font-medium"> Assign tool</span>
                  </div>
                </div>
              </div>
            }
          </div>
        </div>
      }
    </div>
  </div>

  <app-mobile-drawer
    [visible]="viewListTool()"
    [direction]="'btt'"
    [size]="'60%'"
    (visibleChange)="closeListToolAssign()"
  >
    @if (viewListTool()) {
      <div class="w-full h-full flex flex-col">
        <!--  header-->
        <div
          class="fixed z-50 w-full h-12 px-4 py-3 flex items-center justify-between bg-base-200 dark:bg-dark-base-200 text-[16px] font-bold text-base-content dark:text-dark-base-content"
        >
          <app-svg-icon
            type="icClose"
            class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
            (click)="closeListToolAssign()"
          ></app-svg-icon>
          Assign Tool
          <app-svg-icon
            type="icPlus"
            class="w-6 h-6 !text-primary dark:!text-dark-primary cursor-pointer"
            (click)="openCreateToolDialog()"
          ></app-svg-icon>
        </div>

        <div
          class="pt-16 pb-42 flex-1 w-full flex flex-col bg-base-100 dark:bg-dark-base-100"
        >
          <!-- Fixed Search Box -->
          <div
            class="fixed top-12 left-0 right-0 z-40 bg-base-100 dark:bg-dark-base-100 p-4 border-b border-primary-border dark:border-dark-primary-border"
          >
            <dx-form-field
              class="flex items-center justify-start"
              [style.margin-bottom]="0"
              [style.--dx-form-field-label-offset-y]="0"
              [subscriptHidden]="true"
            >
              <app-svg-icon
                dxPrefix
                type="icSearch"
                class="w-6 h-6 ml-3 !text-neutral-content dark:!text-dark-neutral-content"
              ></app-svg-icon>
              <input
                dxInput
                [type]="'text'"
                [ngModel]="searchToolTerm()"
                (ngModelChange)="searchToolTerm.set($event)"
                placeholder="Search by Name"
              />
            </dx-form-field>
          </div>

          <div class="pt-20 pb-48 px-4 flex-1">
            <div class="list-tools w-full space-y-4">
              <!--  list Tools-->
              @for (tool of filteredTools(); track tool.id) {
                <div
                  class="rounded-2xl flex flex-col border bg-base-400 dark:bg-dark-base-400"
                  [ngClass]="
              isSelectedTool(tool)
                ? 'border-primary dark:border-dark-primary'
                : 'border-primary-border dark:border-dark-primary-border'
            "
                >
                  <div class="p-3 lg:px-6 lg:py-5 flex flex-col">
                    <div class="flex items-center space-x-3">
                      <app-svg-icon
                        type="icTool"
                        class="w-6 h-6 !text-base-content dark:!text-dark-base-content justify-start"
                      ></app-svg-icon>
                      <div
                        class="flex-1 grow truncate text-base-content dark:text-dark-base-content text-base font-bold items-end !cursor-pointer hover:!underline"
                        (click)="onEditFlowTool(tool)"
                      >
                        {{ tool.name }}
                      </div>
                      @if (studioStore.status() === STUDIO_STATUS.DEV) {
                        <div class="flex-none flex items-center justify-end space-x-4">
                          <app-svg-icon
                            type="icMoreHorizontal"
                            class="w-5 h-5 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
                            cdkOverlayOrigin
                            #trigger="cdkOverlayOrigin"
                            (click)="
                      openToolAction.set(
                        openToolAction() ? undefined : tool.id
                      );
                      $event.stopPropagation()
                    "
                          >
                          </app-svg-icon>
                          <ng-template
                            cdkConnectedOverlay
                            [cdkConnectedOverlayOrigin]="trigger"
                            [cdkConnectedOverlayOpen]="openToolAction() === tool.id"
                            [cdkConnectedOverlayPush]="true"
                            [cdkConnectedOverlayPositions]="[
                                {
                                  originX: 'start',
                                  originY: 'center',
                                  overlayX: 'end',
                                  overlayY: 'top',
                                  offsetY: 10,
                                },
                                {
                                  originX: 'start',
                                  originY: 'center',
                                  overlayX: 'end',
                                  overlayY: 'bottom',
                                  offsetY: 10
                                },
                              ]"
                          >
                            <ul
                              class="w-[245px] flex flex-col justify-between p-1 z-50 shadow-lg rounded-xl !text-base-content dark:!text-dark-base-content border border-primary-border dark:border-dark-primary-border bg-base-400 dark:bg-dark-base-400"
                              (clickOutside)="openToolAction.set(undefined)"
                            >
                              <!-- <li>
                                            <button
                                              (click)="
                                                $event.stopPropagation(); onPublishTool(tool)
                                              "
                                                  class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                                                >
                                                  <app-svg-icon
                                                    type="icCheck"
                                                    class="w-6 h-6 flex items-center justify-center"
                                                  ></app-svg-icon>
                                                  <div
                                                    class="flex items-center justify-between text-[16px] font-medium"
                                                  >
                                                    Publish
                                                  </div>
                                                </button>
                                              </li> -->
                              <li>
                                <button
                                  (click)="$event.stopPropagation(); onEditTool(tool)"
                                  class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                                >
                                  <app-svg-icon
                                    type="icEdit"
                                    class="w-6 h-6 flex items-center justify-center"
                                  ></app-svg-icon>
                                  <div
                                    class="flex items-center justify-between text-[16px] font-medium"
                                  >
                                    Edit
                                  </div>
                                </button>
                              </li>
                              <li>
                                <button
                                  (click)="$event.stopPropagation(); onDeleteTool(tool)"
                                  class="flex w-full px-3 py-[10px] space-x-[10px] cursor-pointer rounded-lg hover:bg-base-400-hover dark:hover:bg-dark-base-400-hover"
                                >
                                  <app-svg-icon
                                    type="icTrash"
                                    class="w-6 h-6 flex items-center justify-center"
                                  ></app-svg-icon>
                                  <div
                                    class="flex items-center justify-between text-[16px] font-medium"
                                  >
                                    Delete
                                  </div>
                                </button>
                              </li>
                            </ul>
                          </ng-template>
                          <dx-checkbox
                            labelPosition="after"
                            [ngModel]="isSelectedTool(tool)"
                            (ngModelChange)="onSelectTool($event, tool)"
                          ></dx-checkbox>
                        </div>
                      }
                    </div>
                    <div
                      class="mt-1 ml-9 text-neutral-content dark:text-neutral-content text-[13px] font-normal"
                    >
                      {{ tool.description }}
                    </div>
                  </div>
                </div>
              } @empty {
                <div
                  class="text-neutral-content dark:text-dark-neutral-content flex items-center justify-center text-sm py-4"
                >
                  <span>No data available</span>
                </div>
              }
            </div>
          </div>

          <!-- Fixed Assign Tool Button -->
          @if (studioStore.status() === STUDIO_STATUS.DEV && filteredTools().length
          > 0) {
            <div
              class="fixed bottom-0 left-0 right-0 z-40 p-4 bg-base-100 dark:bg-dark-base-100 border-t border-primary-border dark:border-dark-primary-border"
            >
              <button
                dxLoadingButton
                [loading]="assigningTool()"
                [disabled]="
            listToolIdNeedAssign().length + listToolIdNeedUnAssign().length ===
            0
          "
                (click)="assignTool()"
                class="w-full"
              >
                Assign Tool
              </button>
            </div>
          }
        </div>
      </div>
    }
  </app-mobile-drawer>

}
