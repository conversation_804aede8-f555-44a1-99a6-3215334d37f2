import { Platform } from '@angular/cdk/platform';
import { CommonModule } from '@angular/common';
import {
  Component,
  effect,
  inject,
  input,
  OnD<PERSON>roy,
  OnInit,
} from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { IAiSelect, ICurrentAi } from '@core/models';
import { AiStore, UserAiStore } from '@core/stores';
import {
  DxButton,
  DxDialog,
  DxFormField,
  DxInput,
  DxLabel,
  DxOption,
  DxSelect,
  DxSlider,
  DxSliderThumb,
  DxSlideToggle,
  DxSnackBar,
  DxTooltip,
} from '@dx-ui/ui';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import { heroInformationCircle, heroXMark } from '@ng-icons/heroicons/outline';
import { SvgIconComponent } from '@shared/components';
import { IAiConfig } from '@shared/models';
import { AiConfigService, AiService, SettingsService } from '@shared/services';
import { CommonUtils } from '@shared/utils';
import { DeleteAiDialogComponent } from '@views/settings/components/ai-settings/delete-ai-dialog/delete-ai-dialog.component';
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  Subject,
  switchMap,
  takeUntil,
  tap,
} from 'rxjs';

@Component({
  selector: 'app-ai-settings',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    DxTooltip,
    DxFormField,
    DxLabel,
    DxInput,
    DxSelect,
    DxOption,
    DxSlideToggle,
    DxSlider,
    DxSliderThumb,
    DxButton,
    NgIconsModule,
    SvgIconComponent,
  ],
  templateUrl: './ai-settings.component.html',
  providers: [provideIcons({ heroInformationCircle, heroXMark })],
})
export class AiSettingsComponent implements OnInit, OnDestroy {
  settings = input<any>();
  aiConfig = input<IAiConfig | undefined>();

  listReplyLength: any[] = [
    { name: 'Short', code: 0 },
    { name: 'Normal', code: 1 },
    { name: 'Longer', code: 2 },
  ];
  languageOptions = [
    { value: 'vi', label: 'Vietnamese' },
    { value: 'en', label: 'English' },
    { value: 'zh-cn', label: 'Chinese (Simplified)' },
    { value: 'zh-tw', label: 'Chinese (Traditional)' },
    { value: 'ja', label: 'Japanese' },
    { value: 'ko', label: 'Korean' },
    { value: 'ms', label: 'Malay' },
    { value: 'th', label: 'Thai' },
    { value: 'es', label: 'Spanish' },
    { value: 'pt', label: 'Portuguese' },
    { value: 'ru', label: 'Russian' },
  ];
  deleteAisDialogRef: any;

  private destroy$ = new Subject<void>();

  private platform = inject(Platform);
  private fb = inject(FormBuilder);
  private dialog = inject(DxDialog);
  private snackBar = inject(DxSnackBar);
  private settingsService = inject(SettingsService);
  private aiConfigService = inject(AiConfigService);
  private aiService = inject(AiService);
  private aiStore = inject(AiStore);
  private userAiStore = inject(UserAiStore);

  // FORM CONTROL
  name = new FormControl<string>('');
  business = new FormControl<string>('');
  description = new FormControl<string>('');
  replyLength = new FormControl<number>(0);
  askForEmailEnabled = new FormControl<boolean>(false);
  usingEmoji = new FormControl<boolean>(false);
  threshold = new FormControl<number>(0);
  bot_style = new FormControl<string>('');
  customPrompt = new FormControl<string>('');
  show_source = new FormControl<boolean>(false);
  speechRoles = new FormControl<
    Array<{ language: string; male: string; female: string; term: string }>
  >([]);

  constructor() {
    effect(() => {
      const currentSettings = this.settings();
      if (currentSettings) {
        this.updateSettingsFormControls(currentSettings);
      }
    });

    effect(() => {
      const currentAiConfig = this.aiConfig();
      if (currentAiConfig) {
        this.updateAiConfigFormControls(currentAiConfig);
      }
    });
  }

  ngOnInit(): void {
    this.initializeFormSubscriptions();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  showDialogConfirmDeleteAi() {
    this.settingsService.getListSetting().subscribe((res) => {
      if (res && res.length > 0) {
        this.deleteAisDialogRef = this.dialog
          .open(DeleteAiDialogComponent, {
            data: {
              name_ai: this.userAiStore.currentAi()?.name,
              canDelete: res.length > 1,
              content:
                res.length > 1
                  ? 'This AI Assistant will be deleted and all conversations with your AI will be lost. This cannot be undone.'
                  : 'You cannot delete this AI Assistant because this is the only AI Assistant on your account.',
            },
            width: '30vw',
            minWidth: '340px',
          })
          .afterClosed()
          .subscribe((res) => {
            if (!!res) {
              this.reloadAiList();
              window.location.reload();
            }
          });
      }
    });
  }

  updateTextAreaHeight(id: string) {
    if (this.platform.isBrowser) {
      const textarea = document.getElementById(id);
      if (textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = textarea.scrollHeight + 32 + 'px';
      }
    }
  }

  addSpeechRole() {
    const roles = this.speechRoles.value ? [...this.speechRoles.value] : [];
    roles.push({
      language: '',
      male: '',
      female: '',
      term: '',
    });
    this.speechRoles.setValue(roles);
  }

  changeSpeechRole(event: any, index: number, key: string) {
    const roles = this.speechRoles.value ? [...this.speechRoles.value] : [];

    if (!roles[index]) {
      roles[index] = {
        language: '',
        male: '',
        female: '',
        term: '',
      };
    }

    roles[index] = { ...roles[index], [key]: event };
    this.speechRoles.setValue(roles);
  }

  deleteSpeechRole(index: number) {
    const roles = this.speechRoles.value ? [...this.speechRoles.value] : [];
    roles.splice(index, 1);
    this.speechRoles.setValue(roles);
  }

  private updateSettingsFormControls(settings: any) {
    this.name.setValue(settings?.name, { emitEvent: false });
    this.business.setValue(settings?.settings?.human_handoff?.business, {
      emitEvent: false,
    });
    this.description.setValue(settings?.description, { emitEvent: false });
    this.replyLength.setValue(settings?.settings?.human_handoff?.reply_length, {
      emitEvent: false,
    });
    this.askForEmailEnabled.setValue(
      settings?.settings?.basic?.ask_for_email?.enabled,
      { emitEvent: false }
    );
    this.usingEmoji.setValue(settings?.settings?.basic?.using_emoji?.enabled, {
      emitEvent: false,
    });
    this.threshold.setValue(
      settings?.settings?.llm_setting?.check_rag?.threshold,
      { emitEvent: false }
    );
    this.customPrompt.setValue(
      settings?.settings?.human_handoff.custom_prompt,
      { emitEvent: false }
    );
    this.speechRoles.setValue(
      settings?.settings?.speech_roles?.map((role: any) => ({
        language: role.language ?? '',
        male: role.male ?? '',
        female: role.female ?? '',
        term: role.term ?? '',
      })) ?? [],
      { emitEvent: false }
    );
  }

  private updateAiConfigFormControls(aiConfig: IAiConfig) {
    this.bot_style.setValue(aiConfig?.bot_style, { emitEvent: false });
    this.show_source.setValue(aiConfig?.show_source, { emitEvent: false });
  }

  private subscribeToFormControlWithHandler(
    control: FormControl,
    handler: (value: any) => void,
    shouldFilter: ((value: any) => boolean) | undefined = undefined
  ): void {
    let stream = control.valueChanges.pipe(
      takeUntil(this.destroy$),
      distinctUntilChanged(),
      debounceTime(500)
    );

    if (shouldFilter) {
      stream = stream.pipe(filter(shouldFilter));
    }

    stream.subscribe({ next: handler });
  }

  private subscribeToFormControlSettings(
    control: FormControl,
    shouldFilter: ((value: any) => boolean) | undefined = undefined
  ): void {
    this.subscribeToFormControlWithHandler(
      control,
      this.buildUpdatedSettings.bind(this),
      shouldFilter
    );
  }

  private subscribeToFormControlAiConfig(
    control: FormControl,
    shouldFilter: ((value: any) => boolean) | undefined = undefined
  ): void {
    this.subscribeToFormControlWithHandler(
      control,
      this.buildUpdatedAiConfig.bind(this),
      shouldFilter
    );
  }

  private initializeFormSubscriptions(): void {
    this.subscribeToFormControlWithHandler(
      this.name,
      this.handleNameChange.bind(this),
      (value: string) => !!value
    );
    this.subscribeToFormControlSettings(this.business);
    this.subscribeToFormControlWithHandler(
      this.description,
      this.handleDescriptionChange.bind(this)
    );
    this.subscribeToFormControlSettings(this.replyLength);
    this.subscribeToFormControlSettings(this.askForEmailEnabled);
    this.subscribeToFormControlSettings(this.usingEmoji);
    this.subscribeToFormControlWithHandler(
      this.threshold,
      this.handleThresholdChange.bind(this),
      (value: number) => value >= 0 && value <= 1
    );
    this.subscribeToFormControlSettings(this.speechRoles);
    this.subscribeToFormControlSettings(this.customPrompt);
    this.subscribeToFormControlAiConfig(this.bot_style);
    this.subscribeToFormControlAiConfig(this.show_source);
  }

  private handleNameChange() {
    this.reloadAiList();
    this.updateAiInfo();
  }

  private handleDescriptionChange() {
    this.updateAiInfo();
  }

  private handleThresholdChange(value: number) {
    this.buildUpdatedAiConfig();
    this.threshold.setValue(value, { emitEvent: false });
  }

  private buildUpdatedSettings(): void {
    const currentSettings = this.settings()?.settings;
    const updatedSettings = {
      ...currentSettings,
      human_handoff: {
        ...currentSettings?.human_handoff,
        business: this.business.value,
        reply_length: this.replyLength.value,
        ask_for_email: {
          ...currentSettings?.human_handoff?.ask_for_email,
          enabled: this.askForEmailEnabled.value,
        },
        custom_prompt: this.customPrompt.value,
      },
      basic: {
        ...currentSettings?.basic,
        using_emoji: {
          ...currentSettings?.basic?.using_emoji,
          enabled: this.usingEmoji.value,
        },
      },
      llm_setting: {
        ...currentSettings?.llm_setting,
        check_rag: {
          ...currentSettings?.llm_setting?.check_rag,
          threshold: this.threshold.value,
        },
      },
      speech_roles: this.speechRoles.value ?? [],
    };
    this.updateSettings(updatedSettings);
  }

  private buildUpdatedAiConfig(): void {
    const currentAiConfig = this.aiConfig();
    const updatedAiConfig = {
      ...currentAiConfig,
      bot_style: this.bot_style.value ?? '',
      show_source: this.show_source.value ?? false,
    };
    this.updateAiConfig(updatedAiConfig);
  }

  private updateAiInfo() {
    this.settingsService
      .updateAI({
        name: this.name.value,
        description: this.description.value,
      })
      .subscribe({
        next: (res) => {
          if (res) {
            this.snackBar.open(res.message, '', {
              panelClass: 'dx-snack-bar-success',
              duration: 5000,
              verticalPosition: 'top',
              horizontalPosition: 'right',
            });
            const currentAi = this.userAiStore.currentAi();
            if (currentAi && currentAi.name !== this.name.value) {
              this.userAiStore.setCurrentAi({
                ...currentAi,
                name: this.name.value || '',
                description: this.description.value || '',
              });
            }
          }
        },
        error: (err) => {
          this.snackBar.open(err.error.detail, '', {
            panelClass: 'dx-snack-bar-error',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
        },
      });
  }

  private updateSettings(data: any) {
    this.settingsService.updateSetting(data).subscribe({
      next: (res) => {
        if (res) {
          this.snackBar.open(res.message, '', {
            panelClass: 'dx-snack-bar-success',
            duration: 5000,
            verticalPosition: 'top',
            horizontalPosition: 'right',
          });
        }
      },
      error: (err) => {
        this.snackBar.open(err.error.detail, '', {
          panelClass: 'dx-snack-bar-error',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
      },
    });
  }

  private updateAiConfig(data: Partial<IAiConfig>) {
    this.aiConfigService.saveAiConfig(data).subscribe({
      next: (_) => {
        this.snackBar.open('AI updated successfully', '', {
          panelClass: 'dx-snack-bar-success',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
      },
      error: (err) => {
        this.snackBar.open(err.error.detail, '', {
          panelClass: 'dx-snack-bar-error',
          duration: 5000,
          verticalPosition: 'top',
          horizontalPosition: 'right',
        });
      },
    });
  }

  private reloadAiList() {
    this.aiService
      .getListAis()
      .pipe(
        tap((ais: ICurrentAi[]) => {
          this.aiStore.setAis(ais);
          this.aiStore.setAisFilter(ais);
        }),
        switchMap((ais: ICurrentAi[]) => {
          const defaultAiId = ais.find((ai) => ai.default)?.id;
          const selectedAiId =
            this.userAiStore.currentAiId() || defaultAiId || ais[0]?.id || '';
          return this.aiService.selectAi(selectedAiId);
        })
      )
      .subscribe({
        next: (ai: IAiSelect) => {
          this.userAiStore.setCurrentAi(ai?.current_ai);
          const storage = CommonUtils.isRemember()
            ? localStorage
            : sessionStorage;
          storage.setItem('current-ai-id', ai.current_ai.id);
        },
      });
  }
}
